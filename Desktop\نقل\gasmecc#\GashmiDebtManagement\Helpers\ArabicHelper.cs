using System.Globalization;

namespace GashmiDebtManagement.Helpers
{
    /// <summary>
    /// مساعد للتعامل مع اللغة العربية والتنسيق
    /// </summary>
    public static class ArabicHelper
    {
        /// <summary>
        /// الثقافة العربية
        /// </summary>
        public static readonly CultureInfo ArabicCulture = new CultureInfo("ar-SA");

        /// <summary>
        /// تنسيق التاريخ العربي
        /// </summary>
        public static string FormatArabicDate(DateTime date)
        {
            return date.ToString("dd/MM/yyyy", ArabicCulture);
        }

        /// <summary>
        /// تنسيق التاريخ والوقت العربي
        /// </summary>
        public static string FormatArabicDateTime(DateTime dateTime)
        {
            return dateTime.ToString("dd/MM/yyyy HH:mm", ArabicCulture);
        }

        /// <summary>
        /// تنسيق المبلغ بالعملة العربية
        /// </summary>
        public static string FormatCurrency(decimal amount, string currencySymbol = "ريال")
        {
            return $"{amount.ToString("N2", ArabicCulture)} {currencySymbol}";
        }

        /// <summary>
        /// تنسيق الرقم العربي
        /// </summary>
        public static string FormatNumber(decimal number)
        {
            return number.ToString("N2", ArabicCulture);
        }

        /// <summary>
        /// تنسيق النسبة المئوية
        /// </summary>
        public static string FormatPercentage(decimal percentage)
        {
            return percentage.ToString("P2", ArabicCulture);
        }

        /// <summary>
        /// تحويل الأرقام الإنجليزية إلى عربية
        /// </summary>
        public static string ConvertToArabicNumbers(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var arabicNumbers = new char[] { '٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩' };
            var result = input.ToCharArray();

            for (int i = 0; i < result.Length; i++)
            {
                if (char.IsDigit(result[i]))
                {
                    result[i] = arabicNumbers[result[i] - '0'];
                }
            }

            return new string(result);
        }

        /// <summary>
        /// تحويل الأرقام العربية إلى إنجليزية
        /// </summary>
        public static string ConvertToEnglishNumbers(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var arabicNumbers = new char[] { '٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩' };
            var result = input.ToCharArray();

            for (int i = 0; i < result.Length; i++)
            {
                var index = Array.IndexOf(arabicNumbers, result[i]);
                if (index >= 0)
                {
                    result[i] = (char)('0' + index);
                }
            }

            return new string(result);
        }

        /// <summary>
        /// الحصول على اسم الشهر العربي
        /// </summary>
        public static string GetArabicMonthName(int month)
        {
            var monthNames = new string[]
            {
                "", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            };

            return month >= 1 && month <= 12 ? monthNames[month] : "";
        }

        /// <summary>
        /// الحصول على اسم اليوم العربي
        /// </summary>
        public static string GetArabicDayName(DayOfWeek dayOfWeek)
        {
            return dayOfWeek switch
            {
                DayOfWeek.Sunday => "الأحد",
                DayOfWeek.Monday => "الاثنين",
                DayOfWeek.Tuesday => "الثلاثاء",
                DayOfWeek.Wednesday => "الأربعاء",
                DayOfWeek.Thursday => "الخميس",
                DayOfWeek.Friday => "الجمعة",
                DayOfWeek.Saturday => "السبت",
                _ => ""
            };
        }

        /// <summary>
        /// تنسيق فترة الأسبوع
        /// </summary>
        public static string FormatWeekPeriod(DateTime fromDate, DateTime toDate)
        {
            return $"من {FormatArabicDate(fromDate)} إلى {FormatArabicDate(toDate)}";
        }

        /// <summary>
        /// تنسيق النص للعرض من اليمين لليسار
        /// </summary>
        public static string FormatRTL(string text, int width = 50)
        {
            if (string.IsNullOrEmpty(text))
                return new string(' ', width);

            if (text.Length >= width)
                return text.Substring(0, width);

            var padding = width - text.Length;
            return new string(' ', padding) + text;
        }

        /// <summary>
        /// تنسيق الجدول العربي
        /// </summary>
        public static string CreateTableSeparator(int width)
        {
            return new string('─', width);
        }

        /// <summary>
        /// تنسيق عنوان الجدول
        /// </summary>
        public static string FormatTableHeader(string title, int width)
        {
            if (string.IsNullOrEmpty(title))
                return new string(' ', width);

            var padding = (width - title.Length) / 2;
            var leftPadding = padding;
            var rightPadding = width - title.Length - leftPadding;

            return new string(' ', leftPadding) + title + new string(' ', rightPadding);
        }
    }
}
