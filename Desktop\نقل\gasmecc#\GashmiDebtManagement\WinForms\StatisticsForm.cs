using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Services;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج الإحصائيات
    /// </summary>
    public partial class StatisticsForm : Form
    {
        private readonly IRaayahRepository _raayahRepository;
        private readonly IWeeklyDebtsRepository _weeklyDebtsRepository;

        // عناصر التحكم
        private Panel? generalStatsPanel;
        private Panel? monthlyStatsPanel;
        private Panel? branchesStatsPanel;
        private Button? refreshButton;

        public StatisticsForm(IRaayahRepository raayahRepository, IWeeklyDebtsRepository weeklyDebtsRepository)
        {
            _raayahRepository = raayahRepository;
            _weeklyDebtsRepository = weeklyDebtsRepository;
            InitializeComponent();
            LoadStatistics();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "الإحصائيات العامة";
            this.Size = new Size(1000, 700);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);

            // زر التحديث
            refreshButton = new Button();
            refreshButton.Text = "تحديث الإحصائيات";
            refreshButton.Size = new Size(150, 40);
            refreshButton.Location = new Point(20, 20);
            refreshButton.BackColor = Color.LightGreen;
            refreshButton.Click += RefreshButton_Click;

            // إنشاء لوحات الإحصائيات
            CreateGeneralStatsPanel();
            CreateMonthlyStatsPanel();
            CreateBranchesStatsPanel();

            this.Controls.AddRange(new Control[] {
                refreshButton, generalStatsPanel, monthlyStatsPanel, branchesStatsPanel
            });

            this.ResumeLayout(false);
        }

        private void CreateGeneralStatsPanel()
        {
            generalStatsPanel = new Panel();
            generalStatsPanel.Location = new Point(20, 80);
            generalStatsPanel.Size = new Size(450, 200);
            generalStatsPanel.BackColor = Color.LightBlue;
            generalStatsPanel.BorderStyle = BorderStyle.FixedSingle;

            var titleLabel = new Label();
            titleLabel.Text = "الإحصائيات العامة";
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(200, 30);
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.ForeColor = Color.DarkBlue;

            generalStatsPanel.Controls.Add(titleLabel);
        }

        private void CreateMonthlyStatsPanel()
        {
            monthlyStatsPanel = new Panel();
            monthlyStatsPanel.Location = new Point(500, 80);
            monthlyStatsPanel.Size = new Size(450, 200);
            monthlyStatsPanel.BackColor = Color.LightYellow;
            monthlyStatsPanel.BorderStyle = BorderStyle.FixedSingle;

            var titleLabel = new Label();
            titleLabel.Text = "إحصائيات الشهر الحالي";
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(200, 30);
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.ForeColor = Color.DarkGreen;

            monthlyStatsPanel.Controls.Add(titleLabel);
        }

        private void CreateBranchesStatsPanel()
        {
            branchesStatsPanel = new Panel();
            branchesStatsPanel.Location = new Point(20, 300);
            branchesStatsPanel.Size = new Size(930, 300);
            branchesStatsPanel.BackColor = Color.LightGreen;
            branchesStatsPanel.BorderStyle = BorderStyle.FixedSingle;

            var titleLabel = new Label();
            titleLabel.Text = "إحصائيات الفروع";
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(200, 30);
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.ForeColor = Color.DarkRed;

            branchesStatsPanel.Controls.Add(titleLabel);
        }

        private async Task LoadStatistics()
        {
            try
            {
                await LoadGeneralStatistics();
                await LoadMonthlyStatistics();
                await LoadBranchesStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإحصائيات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadGeneralStatistics()
        {
            var stats = await _raayahRepository.GetStatisticsAsync();
            var totalDebts = await _weeklyDebtsRepository.CountAsync();

            // مسح المحتوى السابق
            generalStatsPanel!.Controls.Clear();

            var titleLabel = new Label();
            titleLabel.Text = "الإحصائيات العامة";
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(200, 30);
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.ForeColor = Color.DarkBlue;

            var statsText = $@"إجمالي عدد الرعية: {stats.Total}
المفعلين لخصم الحوالة: {stats.WithDiscount}
في كشف الأوزري: {stats.InOzri}
في خارج الكشف: {stats.InKharij}
إجمالي السجلات الأسبوعية: {totalDebts}";

            var statsLabel = new Label();
            statsLabel.Text = statsText;
            statsLabel.Location = new Point(10, 50);
            statsLabel.Size = new Size(400, 140);
            statsLabel.Font = new Font("Tahoma", 11F);

            generalStatsPanel.Controls.AddRange(new Control[] { titleLabel, statsLabel });
        }

        private async Task LoadMonthlyStatistics()
        {
            var currentMonth = DateTime.Now;
            var monthStart = new DateTime(currentMonth.Year, currentMonth.Month, 1);
            var monthEnd = monthStart.AddMonths(1).AddDays(-1);

            var monthlyStats = await _weeklyDebtsRepository.GetPeriodStatisticsAsync(monthStart, monthEnd);

            // مسح المحتوى السابق
            monthlyStatsPanel!.Controls.Clear();

            var titleLabel = new Label();
            titleLabel.Text = $"إحصائيات {currentMonth:MMMM yyyy}";
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(200, 30);
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.ForeColor = Color.DarkGreen;

            var statsText = $@"إجمالي الديون: {monthlyStats.TotalDebts:N2}
إجمالي الواصل: {monthlyStats.TotalReceived:N2}
إجمالي الخصومات: {monthlyStats.TotalDiscount:N2}
الصافي: {monthlyStats.TotalNet:N2}";

            var statsLabel = new Label();
            statsLabel.Text = statsText;
            statsLabel.Location = new Point(10, 50);
            statsLabel.Size = new Size(400, 140);
            statsLabel.Font = new Font("Tahoma", 11F);

            monthlyStatsPanel.Controls.AddRange(new Control[] { titleLabel, statsLabel });
        }

        private async Task LoadBranchesStatistics()
        {
            var currentMonth = DateTime.Now;
            var monthStart = new DateTime(currentMonth.Year, currentMonth.Month, 1);
            var monthEnd = monthStart.AddMonths(1).AddDays(-1);

            var branches = await _weeklyDebtsRepository.GetBranchesTotalsAsync(monthStart, monthEnd);
            var totalBranches = branches.Samir + branches.Maher + branches.Raid + branches.Haider;
            var grandTotal = totalBranches + branches.Late;

            // مسح المحتوى السابق
            branchesStatsPanel!.Controls.Clear();

            var titleLabel = new Label();
            titleLabel.Text = $"إحصائيات الفروع - {currentMonth:MMMM yyyy}";
            titleLabel.Location = new Point(10, 10);
            titleLabel.Size = new Size(300, 30);
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.ForeColor = Color.DarkRed;

            // إنشاء جدول للفروع
            var branchesGrid = new DataGridView();
            branchesGrid.Location = new Point(10, 50);
            branchesGrid.Size = new Size(900, 200);
            branchesGrid.AutoGenerateColumns = false;
            branchesGrid.ReadOnly = true;
            branchesGrid.AllowUserToAddRows = false;

            // إعداد الأعمدة
            var branchColumn = new DataGridViewTextBoxColumn();
            branchColumn.HeaderText = "الفرع";
            branchColumn.Width = 150;

            var amountColumn = new DataGridViewTextBoxColumn();
            amountColumn.HeaderText = "المبلغ";
            amountColumn.Width = 200;
            amountColumn.DefaultCellStyle.Format = "N2";
            amountColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            var percentageColumn = new DataGridViewTextBoxColumn();
            percentageColumn.HeaderText = "النسبة من الإجمالي";
            percentageColumn.Width = 200;
            percentageColumn.DefaultCellStyle.Format = "P2";
            percentageColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            branchesGrid.Columns.AddRange(new DataGridViewColumn[] {
                branchColumn, amountColumn, percentageColumn
            });

            // إضافة البيانات
            branchesGrid.Rows.Add("سمير", branches.Samir, grandTotal > 0 ? branches.Samir / grandTotal : 0);
            branchesGrid.Rows.Add("ماهر", branches.Maher, grandTotal > 0 ? branches.Maher / grandTotal : 0);
            branchesGrid.Rows.Add("رايد", branches.Raid, grandTotal > 0 ? branches.Raid / grandTotal : 0);
            branchesGrid.Rows.Add("حيدر", branches.Haider, grandTotal > 0 ? branches.Haider / grandTotal : 0);
            branchesGrid.Rows.Add("المتأخر", branches.Late, grandTotal > 0 ? branches.Late / grandTotal : 0);
            branchesGrid.Rows.Add("الإجمالي", grandTotal, 1.0);

            // تنسيق الصف الأخير
            branchesGrid.Rows[branchesGrid.Rows.Count - 1].DefaultCellStyle.BackColor = Color.LightYellow;
            branchesGrid.Rows[branchesGrid.Rows.Count - 1].DefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            branchesStatsPanel.Controls.AddRange(new Control[] { titleLabel, branchesGrid });
        }

        private async void RefreshButton_Click(object? sender, EventArgs e)
        {
            refreshButton!.Enabled = false;
            refreshButton.Text = "جاري التحديث...";

            try
            {
                await LoadStatistics();
                MessageBox.Show("تم تحديث الإحصائيات بنجاح!", "تم التحديث",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الإحصائيات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                refreshButton.Enabled = true;
                refreshButton.Text = "تحديث الإحصائيات";
            }
        }
    }
}
