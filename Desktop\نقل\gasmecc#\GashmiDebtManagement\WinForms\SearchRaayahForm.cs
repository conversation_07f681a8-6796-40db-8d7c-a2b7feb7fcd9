using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج البحث عن الرعوي
    /// </summary>
    public partial class SearchRaayahForm : Form
    {
        private readonly IRaayahRepository _raayahRepository;

        // عناصر التحكم
        private Label? searchLabel;
        private TextBox? searchTextBox;
        private Button? searchButton;
        private Button? clearButton;
        private DataGridView? resultsDataGridView;
        private Label? resultsCountLabel;
        private Panel? searchPanel;
        private Panel? resultsPanel;

        public SearchRaayahForm(IRaayahRepository raayahRepository)
        {
            _raayahRepository = raayahRepository;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "البحث عن رعوي";
            this.Size = new Size(800, 600);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);

            // إنشاء لوحة البحث
            CreateSearchPanel();

            // إنشاء لوحة النتائج
            CreateResultsPanel();

            this.ResumeLayout(false);
        }

        private void CreateSearchPanel()
        {
            searchPanel = new Panel();
            searchPanel.Height = 80;
            searchPanel.Dock = DockStyle.Top;
            searchPanel.BackColor = Color.LightBlue;

            // تسمية البحث
            searchLabel = new Label();
            searchLabel.Text = "البحث في أسماء الرعية:";
            searchLabel.Location = new Point(600, 25);
            searchLabel.Size = new Size(150, 23);
            searchLabel.TextAlign = ContentAlignment.MiddleRight;

            // مربع نص البحث
            searchTextBox = new TextBox();
            searchTextBox.Location = new Point(300, 25);
            searchTextBox.Size = new Size(290, 23);
            searchTextBox.KeyDown += SearchTextBox_KeyDown;

            // زر البحث
            searchButton = new Button();
            searchButton.Text = "بحث";
            searchButton.Size = new Size(80, 30);
            searchButton.Location = new Point(210, 23);
            searchButton.BackColor = Color.LightGreen;
            searchButton.Click += SearchButton_Click;

            // زر مسح
            clearButton = new Button();
            clearButton.Text = "مسح";
            clearButton.Size = new Size(80, 30);
            clearButton.Location = new Point(120, 23);
            clearButton.BackColor = Color.LightYellow;
            clearButton.Click += ClearButton_Click;

            searchPanel.Controls.AddRange(new Control[] {
                searchLabel, searchTextBox, searchButton, clearButton
            });

            this.Controls.Add(searchPanel);
        }

        private void CreateResultsPanel()
        {
            resultsPanel = new Panel();
            resultsPanel.Dock = DockStyle.Fill;

            // تسمية عدد النتائج
            resultsCountLabel = new Label();
            resultsCountLabel.Text = "أدخل نص للبحث...";
            resultsCountLabel.Height = 30;
            resultsCountLabel.Dock = DockStyle.Top;
            resultsCountLabel.BackColor = Color.LightGray;
            resultsCountLabel.TextAlign = ContentAlignment.MiddleCenter;
            resultsCountLabel.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            // جدول النتائج
            resultsDataGridView = new DataGridView();
            resultsDataGridView.Dock = DockStyle.Fill;
            resultsDataGridView.AutoGenerateColumns = false;
            resultsDataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            resultsDataGridView.MultiSelect = false;
            resultsDataGridView.ReadOnly = true;
            resultsDataGridView.AllowUserToAddRows = false;
            resultsDataGridView.AllowUserToDeleteRows = false;
            resultsDataGridView.BackgroundColor = Color.White;
            resultsDataGridView.Font = new Font("Tahoma", 10F);

            // إعداد الأعمدة
            SetupDataGridViewColumns();

            // أحداث
            resultsDataGridView.CellDoubleClick += ResultsDataGridView_CellDoubleClick;

            resultsPanel.Controls.AddRange(new Control[] {
                resultsCountLabel, resultsDataGridView
            });

            this.Controls.Add(resultsPanel);
        }

        private void SetupDataGridViewColumns()
        {
            var idColumn = new DataGridViewTextBoxColumn();
            idColumn.Name = "Id";
            idColumn.HeaderText = "الرقم";
            idColumn.DataPropertyName = "Id";
            idColumn.Width = 80;

            var nameColumn = new DataGridViewTextBoxColumn();
            nameColumn.Name = "FullName";
            nameColumn.HeaderText = "الاسم الكامل";
            nameColumn.DataPropertyName = "FullName";
            nameColumn.Width = 300;

            var discountColumn = new DataGridViewCheckBoxColumn();
            discountColumn.Name = "EnableDiscount";
            discountColumn.HeaderText = "خصم الحوالة";
            discountColumn.DataPropertyName = "EnableDiscount";
            discountColumn.Width = 100;

            var ozriColumn = new DataGridViewCheckBoxColumn();
            ozriColumn.Name = "InKashfOzri";
            ozriColumn.HeaderText = "كشف الأوزري";
            ozriColumn.DataPropertyName = "InKashfOzri";
            ozriColumn.Width = 100;

            var kharijColumn = new DataGridViewCheckBoxColumn();
            kharijColumn.Name = "InKharijKashf";
            kharijColumn.HeaderText = "خارج الكشف";
            kharijColumn.DataPropertyName = "InKharijKashf";
            kharijColumn.Width = 100;

            resultsDataGridView!.Columns.AddRange(new DataGridViewColumn[] {
                idColumn, nameColumn, discountColumn, ozriColumn, kharijColumn
            });

            // تنسيق الرؤوس
            resultsDataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Navy;
            resultsDataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            resultsDataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            resultsDataGridView.ColumnHeadersHeight = 35;

            // تنسيق الصفوف
            resultsDataGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.LightBlue;
            resultsDataGridView.RowsDefaultCellStyle.BackColor = Color.White;
            resultsDataGridView.RowTemplate.Height = 30;
        }

        private async void SearchButton_Click(object? sender, EventArgs e)
        {
            await PerformSearch();
        }

        private void ClearButton_Click(object? sender, EventArgs e)
        {
            searchTextBox!.Clear();
            resultsDataGridView!.DataSource = null;
            resultsCountLabel!.Text = "أدخل نص للبحث...";
            searchTextBox.Focus();
        }

        private async void SearchTextBox_KeyDown(object? sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                await PerformSearch();
            }
        }

        private async Task PerformSearch()
        {
            try
            {
                var searchTerm = searchTextBox?.Text?.Trim();

                if (string.IsNullOrEmpty(searchTerm))
                {
                    MessageBox.Show("يرجى إدخال نص للبحث", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    searchTextBox?.Focus();
                    return;
                }

                if (searchTerm.Length < 2)
                {
                    MessageBox.Show("يرجى إدخال حرفين على الأقل للبحث", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    searchTextBox?.Focus();
                    return;
                }

                // تعطيل عناصر التحكم أثناء البحث
                SetControlsEnabled(false);
                resultsCountLabel!.Text = "جاري البحث...";

                // تنفيذ البحث
                var results = await _raayahRepository.FindAsync(r => r.FullName.Contains(searchTerm));
                var resultsList = results.OrderBy(r => r.FullName).ToList();

                // عرض النتائج
                resultsDataGridView!.DataSource = resultsList;

                // تحديث عدد النتائج
                if (resultsList.Count == 0)
                {
                    resultsCountLabel.Text = "لم يتم العثور على نتائج";
                    resultsCountLabel.ForeColor = Color.Red;
                }
                else
                {
                    resultsCountLabel.Text = $"تم العثور على {resultsList.Count} نتيجة";
                    resultsCountLabel.ForeColor = Color.Green;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                resultsCountLabel!.Text = "حدث خطأ في البحث";
                resultsCountLabel.ForeColor = Color.Red;
            }
            finally
            {
                // إعادة تفعيل عناصر التحكم
                SetControlsEnabled(true);
            }
        }

        private void SetControlsEnabled(bool enabled)
        {
            searchTextBox!.Enabled = enabled;
            searchButton!.Enabled = enabled;
            clearButton!.Enabled = enabled;
        }

        private void ResultsDataGridView_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && resultsDataGridView?.SelectedRows.Count > 0)
            {
                var selectedRaayah = resultsDataGridView.SelectedRows[0].DataBoundItem as Raayah;
                if (selectedRaayah != null)
                {
                    // فتح نموذج تعديل الرعوي
                    var editForm = new EditRaayahForm(_raayahRepository, selectedRaayah);
                    editForm.RaayahUpdated += async (s, args) => {
                        // تحديث النتائج بعد التعديل
                        await PerformSearch();
                    };

                    editForm.ShowDialog();
                }
            }
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);

            // تعيين التركيز على مربع البحث
            searchTextBox?.Focus();
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            // معالجة اختصارات لوحة المفاتيح
            switch (keyData)
            {
                case Keys.F3:
                    _ = PerformSearch();
                    return true;

                case Keys.Escape:
                    this.Close();
                    return true;

                case Keys.Control | Keys.F:
                    searchTextBox?.Focus();
                    return true;
            }

            return base.ProcessCmdKey(ref msg, keyData);
        }
    }
}
