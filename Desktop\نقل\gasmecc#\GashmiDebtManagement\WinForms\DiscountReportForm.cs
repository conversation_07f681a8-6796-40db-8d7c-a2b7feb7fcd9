using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Services;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج التقرير مع خصم الحوالة
    /// </summary>
    public partial class DiscountReportForm : Form
    {
        private readonly IWeeklyDebtsRepository _weeklyDebtsRepository;
        
        // عناصر التحكم
        private Panel? filterPanel;
        private Label? titleLabel;
        private Label? fromDateLabel;
        private DateTimePicker? fromDatePicker;
        private Label? toDateLabel;
        private DateTimePicker? toDatePicker;
        private Button? generateButton;
        private Button? exportButton;
        private DataGridView? reportGridView;
        private Panel? summaryPanel;
        private Label? summaryLabel;
        private Label? discountInfoLabel;

        public DiscountReportForm(IWeeklyDebtsRepository weeklyDebtsRepository)
        {
            _weeklyDebtsRepository = weeklyDebtsRepository;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "التقرير مع خصم الحوالة";
            this.Size = new Size(1300, 800);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);

            // إنشاء لوحة التصفية
            CreateFilterPanel();

            // إنشاء جدول التقرير
            CreateReportGridView();

            // إنشاء لوحة الملخص
            CreateSummaryPanel();

            this.ResumeLayout(false);
        }

        private void CreateFilterPanel()
        {
            filterPanel = new Panel();
            filterPanel.Height = 140;
            filterPanel.Dock = DockStyle.Top;
            filterPanel.BackColor = Color.LightBlue;

            // عنوان التقرير
            titleLabel = new Label();
            titleLabel.Text = "التقرير مع خصم الحوالة (3%) - شركة الغشمي";
            titleLabel.Location = new Point(400, 10);
            titleLabel.Size = new Size(500, 30);
            titleLabel.Font = new Font("Tahoma", 16F, FontStyle.Bold);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.ForeColor = Color.DarkBlue;

            // معلومات الخصم
            discountInfoLabel = new Label();
            discountInfoLabel.Text = "خصم الحوالة = (إجمالي الديون - المبلغ الواصل) × 3%";
            discountInfoLabel.Location = new Point(400, 45);
            discountInfoLabel.Size = new Size(500, 25);
            discountInfoLabel.Font = new Font("Tahoma", 12F, FontStyle.Italic);
            discountInfoLabel.TextAlign = ContentAlignment.MiddleCenter;
            discountInfoLabel.ForeColor = Color.DarkRed;

            // تسمية تاريخ البداية
            fromDateLabel = new Label();
            fromDateLabel.Text = "من تاريخ:";
            fromDateLabel.Location = new Point(1100, 80);
            fromDateLabel.Size = new Size(80, 23);
            fromDateLabel.TextAlign = ContentAlignment.MiddleRight;

            // منتقي تاريخ البداية
            fromDatePicker = new DateTimePicker();
            fromDatePicker.Location = new Point(950, 80);
            fromDatePicker.Size = new Size(140, 23);
            fromDatePicker.Format = DateTimePickerFormat.Short;
            fromDatePicker.Value = DateTime.Now.AddDays(-7);

            // تسمية تاريخ النهاية
            toDateLabel = new Label();
            toDateLabel.Text = "إلى تاريخ:";
            toDateLabel.Location = new Point(850, 80);
            toDateLabel.Size = new Size(80, 23);
            toDateLabel.TextAlign = ContentAlignment.MiddleRight;

            // منتقي تاريخ النهاية
            toDatePicker = new DateTimePicker();
            toDatePicker.Location = new Point(700, 80);
            toDatePicker.Size = new Size(140, 23);
            toDatePicker.Format = DateTimePickerFormat.Short;
            toDatePicker.Value = DateTime.Now;

            // زر إنشاء التقرير
            generateButton = new Button();
            generateButton.Text = "إنشاء التقرير";
            generateButton.Size = new Size(120, 35);
            generateButton.Location = new Point(550, 75);
            generateButton.BackColor = Color.LightGreen;
            generateButton.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            generateButton.Click += GenerateButton_Click;

            // زر التصدير
            exportButton = new Button();
            exportButton.Text = "تصدير CSV";
            exportButton.Size = new Size(100, 35);
            exportButton.Location = new Point(440, 75);
            exportButton.BackColor = Color.LightYellow;
            exportButton.Enabled = false;
            exportButton.Click += ExportButton_Click;

            filterPanel.Controls.AddRange(new Control[] {
                titleLabel, discountInfoLabel, fromDateLabel, fromDatePicker, 
                toDateLabel, toDatePicker, generateButton, exportButton
            });

            this.Controls.Add(filterPanel);
        }

        private void CreateReportGridView()
        {
            reportGridView = new DataGridView();
            reportGridView.Dock = DockStyle.Fill;
            reportGridView.AutoGenerateColumns = false;
            reportGridView.ReadOnly = true;
            reportGridView.AllowUserToAddRows = false;
            reportGridView.AllowUserToDeleteRows = false;
            reportGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            reportGridView.BackgroundColor = Color.White;
            reportGridView.Font = new Font("Tahoma", 10F);

            // إعداد الأعمدة
            SetupReportColumns();

            this.Controls.Add(reportGridView);
        }

        private void SetupReportColumns()
        {
            var nameColumn = new DataGridViewTextBoxColumn();
            nameColumn.Name = "RaayahName";
            nameColumn.HeaderText = "اسم الرعوي";
            nameColumn.DataPropertyName = "Raayah.FullName";
            nameColumn.Width = 250;

            var totalDebtsColumn = new DataGridViewTextBoxColumn();
            totalDebtsColumn.Name = "TotalDebts";
            totalDebtsColumn.HeaderText = "إجمالي الديون";
            totalDebtsColumn.DataPropertyName = "TotalDebtsAmount";
            totalDebtsColumn.Width = 150;
            totalDebtsColumn.DefaultCellStyle.Format = "N2";
            totalDebtsColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            totalDebtsColumn.DefaultCellStyle.BackColor = Color.LightYellow;
            totalDebtsColumn.DefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            var receivedColumn = new DataGridViewTextBoxColumn();
            receivedColumn.Name = "ReceivedAmount";
            receivedColumn.HeaderText = "المبلغ الواصل";
            receivedColumn.DataPropertyName = "ReceivedAmount";
            receivedColumn.Width = 150;
            receivedColumn.DefaultCellStyle.Format = "N2";
            receivedColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            var discountColumn = new DataGridViewTextBoxColumn();
            discountColumn.Name = "DiscountAmount";
            discountColumn.HeaderText = "خصم الحوالة (3%)";
            discountColumn.DataPropertyName = "DiscountAmount";
            discountColumn.Width = 150;
            discountColumn.DefaultCellStyle.Format = "N2";
            discountColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            discountColumn.DefaultCellStyle.BackColor = Color.LightCoral;
            discountColumn.DefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            var netColumn = new DataGridViewTextBoxColumn();
            netColumn.Name = "NetAmount";
            netColumn.HeaderText = "الصافي النهائي";
            netColumn.DataPropertyName = "NetAmount";
            netColumn.Width = 150;
            netColumn.DefaultCellStyle.Format = "N2";
            netColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            netColumn.DefaultCellStyle.BackColor = Color.LightGreen;
            netColumn.DefaultCellStyle.Font = new Font("Tahoma", 11F, FontStyle.Bold);

            var discountStatusColumn = new DataGridViewTextBoxColumn();
            discountStatusColumn.Name = "DiscountStatus";
            discountStatusColumn.HeaderText = "حالة الخصم";
            discountStatusColumn.Width = 120;
            discountStatusColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            var detailsColumn = new DataGridViewTextBoxColumn();
            detailsColumn.Name = "Details";
            detailsColumn.HeaderText = "تفاصيل الفروع";
            detailsColumn.Width = 300;
            detailsColumn.DefaultCellStyle.Font = new Font("Tahoma", 9F);

            reportGridView!.Columns.AddRange(new DataGridViewColumn[] {
                nameColumn, totalDebtsColumn, receivedColumn, discountColumn, 
                netColumn, discountStatusColumn, detailsColumn
            });

            // تنسيق الرؤوس
            reportGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Navy;
            reportGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            reportGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 11F, FontStyle.Bold);
            reportGridView.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            reportGridView.ColumnHeadersHeight = 40;

            // تنسيق الصفوف
            reportGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.AliceBlue;
            reportGridView.RowsDefaultCellStyle.BackColor = Color.White;
            reportGridView.RowTemplate.Height = 35;

            // حدث تنسيق الخلايا
            reportGridView.CellFormatting += ReportGridView_CellFormatting;
        }

        private void CreateSummaryPanel()
        {
            summaryPanel = new Panel();
            summaryPanel.Height = 120;
            summaryPanel.Dock = DockStyle.Bottom;
            summaryPanel.BackColor = Color.LightGray;

            // تسمية الملخص
            summaryLabel = new Label();
            summaryLabel.Text = "الملخص: لم يتم إنشاء التقرير بعد";
            summaryLabel.Location = new Point(20, 20);
            summaryLabel.Size = new Size(1200, 80);
            summaryLabel.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            summaryLabel.ForeColor = Color.DarkBlue;

            summaryPanel.Controls.Add(summaryLabel);
            this.Controls.Add(summaryPanel);
        }

        private async void GenerateButton_Click(object? sender, EventArgs e)
        {
            try
            {
                generateButton!.Enabled = false;
                generateButton.Text = "جاري الإنشاء...";

                var fromDate = fromDatePicker!.Value.Date;
                var toDate = toDatePicker!.Value.Date;

                if (fromDate > toDate)
                {
                    MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية", 
                        "خطأ في التاريخ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // تحميل البيانات
                var weeklyData = await _weeklyDebtsRepository.GetByPeriodWithRaayahAsync(fromDate, toDate);
                var reportData = weeklyData.OrderBy(d => d.Raayah.FullName).ToList();

                if (!reportData.Any())
                {
                    MessageBox.Show("لا توجد بيانات للفترة المحددة", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // عرض البيانات
                reportGridView!.DataSource = reportData;

                // تحديث الملخص
                UpdateSummary(reportData, fromDate, toDate);

                // تفعيل زر التصدير
                exportButton!.Enabled = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                generateButton!.Enabled = true;
                generateButton.Text = "إنشاء التقرير";
            }
        }

        private void ReportGridView_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var debt = reportGridView!.Rows[e.RowIndex].DataBoundItem as WeeklyDebts;
                if (debt == null) return;

                switch (reportGridView.Columns[e.ColumnIndex].Name)
                {
                    case "DiscountStatus":
                        e.Value = debt.Raayah.EnableDiscount ? "مفعل" : "معطل";
                        e.CellStyle.ForeColor = debt.Raayah.EnableDiscount ? Color.DarkGreen : Color.DarkRed;
                        e.CellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
                        e.FormattingApplied = true;
                        break;

                    case "Details":
                        e.Value = $"سمير: {debt.SamirAmount:N0} | ماهر: {debt.MaherAmount:N0} | رايد: {debt.RaidAmount:N0} | حيدر: {debt.HaiderAmount:N0} | متأخر: {debt.LateAmount:N0}";
                        e.FormattingApplied = true;
                        break;

                    case "NetAmount":
                        // تلوين الصافي حسب القيمة
                        if (debt.NetAmount > 0)
                            e.CellStyle.ForeColor = Color.DarkGreen;
                        else if (debt.NetAmount < 0)
                            e.CellStyle.ForeColor = Color.DarkRed;
                        break;

                    case "DiscountAmount":
                        // تمييز الخصومات
                        if (debt.DiscountAmount > 0)
                            e.CellStyle.ForeColor = Color.DarkRed;
                        break;
                }
            }
        }

        private void UpdateSummary(List<WeeklyDebts> data, DateTime fromDate, DateTime toDate)
        {
            var totals = CalculationService.CalculatePeriodTotals(data);
            var discountEnabledCount = data.Count(d => d.Raayah.EnableDiscount);
            var discountDisabledCount = data.Count(d => !d.Raayah.EnableDiscount);

            summaryLabel!.Text = $"ملخص التقرير مع خصم الحوالة ({fromDate:dd/MM/yyyy} - {toDate:dd/MM/yyyy})\n" +
                                $"إجمالي الديون: {totals.TotalDebts:N2} ريال | إجمالي الواصل: {totals.TotalReceived:N2} ريال\n" +
                                $"إجمالي خصم الحوالة (3%): {totals.TotalDiscount:N2} ريال | الصافي النهائي: {totals.TotalNet:N2} ريال\n" +
                                $"عدد الرعية: {data.Count} | مفعل للخصم: {discountEnabledCount} | معطل للخصم: {discountDisabledCount}";
        }

        private void ExportButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var data = reportGridView!.DataSource as List<WeeklyDebts>;
                if (data == null || !data.Any())
                {
                    MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var fromDate = fromDatePicker!.Value.Date;
                var toDate = toDatePicker!.Value.Date;

                var filePath = ExportService.ExportDiscountReportToCsv(data, fromDate, toDate);

                var result = MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{filePath}\n\nهل تريد فتح الملف؟", 
                    "تم التصدير", MessageBoxButtons.YesNo, MessageBoxIcon.Information);

                if (result == DialogResult.Yes)
                {
                    System.Diagnostics.Process.Start("notepad.exe", filePath);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في تصدير التقرير:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            
            // تعيين التركيز على زر الإنشاء
            generateButton?.Focus();
        }
    }
}
