using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using GashmiDebtManagement.UI.DesignSystem;

namespace GashmiDebtManagement.UI.Controls
{
    /// <summary>
    /// مربع نص حديث مع تأثيرات بصرية ودعم RTL متقدم
    /// </summary>
    public class ModernTextBox : UserControl
    {
        #region المتغيرات الخاصة

        private TextBox _textBox;
        private Label _labelText;
        private Label _helperText;
        private Panel _underline;
        private bool _isFocused = false;
        private bool _hasError = false;
        private string _placeholderText = "";
        private string _labelTextValue = "";
        private string _helperTextValue = "";
        private Color _accentColor = ModernDesignSystem.Colors.Primary;
        private TextBoxStyle _textBoxStyle = TextBoxStyle.Outlined;
        private System.Windows.Forms.Timer _animationTimer;
        private float _animationProgress = 0f;

        #endregion

        #region الخصائص العامة

        /// <summary>
        /// نص مربع الإدخال
        /// </summary>
        [Category("Appearance")]
        [Description("نص مربع الإدخال")]
        public override string Text
        {
            get => _textBox.Text;
            set => _textBox.Text = value;
        }

        /// <summary>
        /// النص التوضيحي (Placeholder)
        /// </summary>
        [Category("Appearance")]
        [Description("النص التوضيحي الذي يظهر عندما يكون المربع فارغاً")]
        public string PlaceholderText
        {
            get => _placeholderText;
            set
            {
                _placeholderText = value;
                UpdatePlaceholder();
            }
        }

        /// <summary>
        /// نص التسمية
        /// </summary>
        [Category("Appearance")]
        [Description("نص التسمية الذي يظهر أعلى المربع")]
        public string LabelText
        {
            get => _labelTextValue;
            set
            {
                _labelTextValue = value;
                _labelText.Text = value;
                _labelText.Visible = !string.IsNullOrEmpty(value);
                UpdateLayout();
            }
        }

        /// <summary>
        /// نص المساعدة
        /// </summary>
        [Category("Appearance")]
        [Description("نص المساعدة الذي يظهر أسفل المربع")]
        public string HelperText
        {
            get => _helperTextValue;
            set
            {
                _helperTextValue = value;
                _helperText.Text = value;
                _helperText.Visible = !string.IsNullOrEmpty(value);
                UpdateLayout();
            }
        }

        /// <summary>
        /// لون التمييز
        /// </summary>
        [Category("Appearance")]
        [Description("لون التمييز للحدود والتسمية")]
        public Color AccentColor
        {
            get => _accentColor;
            set
            {
                _accentColor = value;
                Invalidate();
            }
        }

        /// <summary>
        /// نمط مربع النص
        /// </summary>
        [Category("Appearance")]
        [Description("نمط مربع النص")]
        public TextBoxStyle TextBoxStyle
        {
            get => _textBoxStyle;
            set
            {
                _textBoxStyle = value;
                UpdateStyle();
            }
        }

        /// <summary>
        /// حالة الخطأ
        /// </summary>
        [Category("Appearance")]
        [Description("إظهار حالة الخطأ")]
        public bool HasError
        {
            get => _hasError;
            set
            {
                _hasError = value;
                UpdateErrorState();
            }
        }

        /// <summary>
        /// نوع المحتوى
        /// </summary>
        [Category("Behavior")]
        [Description("نوع المحتوى (نص، رقم، كلمة مرور، إلخ)")]
        public CharacterCasing CharacterCasing
        {
            get => _textBox.CharacterCasing;
            set => _textBox.CharacterCasing = value;
        }

        /// <summary>
        /// الحد الأقصى لطول النص
        /// </summary>
        [Category("Behavior")]
        [Description("الحد الأقصى لطول النص")]
        public int MaxLength
        {
            get => _textBox.MaxLength;
            set => _textBox.MaxLength = value;
        }

        /// <summary>
        /// نمط كلمة المرور
        /// </summary>
        [Category("Behavior")]
        [Description("رمز كلمة المرور")]
        public char PasswordChar
        {
            get => _textBox.PasswordChar;
            set => _textBox.PasswordChar = value;
        }

        /// <summary>
        /// القراءة فقط
        /// </summary>
        [Category("Behavior")]
        [Description("جعل المربع للقراءة فقط")]
        public bool ReadOnly
        {
            get => _textBox.ReadOnly;
            set => _textBox.ReadOnly = value;
        }

        #endregion

        #region الأحداث

        public new event EventHandler<EventArgs> TextChanged;
        public new event EventHandler<EventArgs> Enter;
        public new event EventHandler<EventArgs> Leave;

        #endregion

        #region البناء والتهيئة

        public ModernTextBox()
        {
            InitializeComponent();
            SetStyle(ControlStyles.AllPaintingInWmPaint | 
                     ControlStyles.UserPaint | 
                     ControlStyles.DoubleBuffer | 
                     ControlStyles.ResizeRedraw, true);

            // تهيئة مؤقت الحركة
            _animationTimer = new System.Windows.Forms.Timer { Interval = 16 }; // 60 FPS
            _animationTimer.Tick += AnimationTimer_Tick;
        }

        private void InitializeComponent()
        {
            // إعدادات أساسية
            Size = new Size(250, ModernDesignSystem.Dimensions.InputHeight + 40);
            BackColor = Color.Transparent;
            RightToLeft = RightToLeft.Yes;

            // إنشاء التسمية
            _labelText = new Label
            {
                Font = ModernDesignSystem.Typography.Caption,
                ForeColor = ModernDesignSystem.Colors.TextSecondary,
                BackColor = Color.Transparent,
                AutoSize = true,
                Visible = false
            };

            // إنشاء مربع النص
            _textBox = new TextBox
            {
                BorderStyle = BorderStyle.None,
                Font = ModernDesignSystem.Typography.InputText,
                ForeColor = ModernDesignSystem.Colors.TextPrimary,
                BackColor = ModernDesignSystem.Colors.Surface,
                RightToLeft = RightToLeft.Yes
            };

            // إنشاء نص المساعدة
            _helperText = new Label
            {
                Font = ModernDesignSystem.Typography.Caption,
                ForeColor = ModernDesignSystem.Colors.TextHint,
                BackColor = Color.Transparent,
                AutoSize = true,
                Visible = false
            };

            // إنشاء الخط السفلي
            _underline = new Panel
            {
                Height = 2,
                BackColor = ModernDesignSystem.Colors.Border
            };

            // إضافة العناصر
            Controls.AddRange(new Control[] { _labelText, _textBox, _helperText, _underline });

            // ربط الأحداث
            _textBox.TextChanged += TextBox_TextChanged;
            _textBox.Enter += TextBox_Enter;
            _textBox.Leave += TextBox_Leave;
            _textBox.KeyDown += TextBox_KeyDown;

            UpdateLayout();
        }

        #endregion

        #region تخطيط العناصر

        private void UpdateLayout()
        {
            var currentY = 0;

            // موضع التسمية
            if (_labelText.Visible)
            {
                _labelText.Location = new Point(0, currentY);
                currentY += _labelText.Height + ModernDesignSystem.Spacing.XSmall;
            }

            // موضع مربع النص
            var textBoxHeight = ModernDesignSystem.Dimensions.InputHeight;
            _textBox.Location = new Point(ModernDesignSystem.Spacing.Medium, currentY + 8);
            _textBox.Size = new Size(Width - (ModernDesignSystem.Spacing.Medium * 2), textBoxHeight - 16);
            
            // موضع الخط السفلي
            _underline.Location = new Point(0, currentY + textBoxHeight - 2);
            _underline.Size = new Size(Width, 2);
            
            currentY += textBoxHeight + ModernDesignSystem.Spacing.XSmall;

            // موضع نص المساعدة
            if (_helperText.Visible)
            {
                _helperText.Location = new Point(0, currentY);
                currentY += _helperText.Height;
            }

            // تحديث ارتفاع العنصر
            Height = currentY + ModernDesignSystem.Spacing.XSmall;
        }

        private void UpdateStyle()
        {
            switch (_textBoxStyle)
            {
                case TextBoxStyle.Outlined:
                    _underline.Visible = false;
                    break;
                case TextBoxStyle.Underlined:
                    _underline.Visible = true;
                    break;
                case TextBoxStyle.Filled:
                    _textBox.BackColor = ModernDesignSystem.Colors.Elevated;
                    _underline.Visible = false;
                    break;
            }
            Invalidate();
        }

        #endregion

        #region الرسم المخصص

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            if (_textBoxStyle == TextBoxStyle.Outlined)
            {
                DrawOutlinedBorder(g);
            }
            else if (_textBoxStyle == TextBoxStyle.Filled)
            {
                DrawFilledBackground(g);
            }
        }

        private void DrawOutlinedBorder(Graphics g)
        {
            var borderColor = GetBorderColor();
            var borderWidth = _isFocused ? 2 : 1;
            
            var textBoxRect = new Rectangle(
                0, 
                _labelText.Visible ? _labelText.Height + ModernDesignSystem.Spacing.XSmall : 0,
                Width - 1, 
                ModernDesignSystem.Dimensions.InputHeight - 1
            );

            using var pen = new Pen(borderColor, borderWidth);
            g.DrawRoundedRectangle(pen, textBoxRect, ModernDesignSystem.Dimensions.BorderRadiusSmall);
        }

        private void DrawFilledBackground(Graphics g)
        {
            var textBoxRect = new Rectangle(
                0, 
                _labelText.Visible ? _labelText.Height + ModernDesignSystem.Spacing.XSmall : 0,
                Width, 
                ModernDesignSystem.Dimensions.InputHeight
            );

            using var brush = new SolidBrush(ModernDesignSystem.Colors.Elevated);
            g.FillRoundedRectangle(brush, textBoxRect, ModernDesignSystem.Dimensions.BorderRadiusSmall);
        }

        private Color GetBorderColor()
        {
            if (_hasError)
                return ModernDesignSystem.Colors.Error;
            if (_isFocused)
                return _accentColor;
            return ModernDesignSystem.Colors.Border;
        }

        #endregion

        #region معالجة الأحداث

        private void TextBox_TextChanged(object sender, EventArgs e)
        {
            UpdatePlaceholder();
            TextChanged?.Invoke(this, e);
        }

        private void TextBox_Enter(object sender, EventArgs e)
        {
            _isFocused = true;
            StartFocusAnimation();
            Enter?.Invoke(this, e);
            Invalidate();
        }

        private void TextBox_Leave(object sender, EventArgs e)
        {
            _isFocused = false;
            StartBlurAnimation();
            Leave?.Invoke(this, e);
            Invalidate();
        }

        private void TextBox_KeyDown(object sender, KeyEventArgs e)
        {
            // معالجة مفاتيح خاصة للعربية
            if (e.KeyCode == Keys.Enter)
            {
                OnEnterPressed();
            }
        }

        protected virtual void OnEnterPressed()
        {
            // يمكن للفئات المشتقة تخصيص هذا السلوك
        }

        #endregion

        #region الحركات والتأثيرات

        private void StartFocusAnimation()
        {
            _animationProgress = 0f;
            _animationTimer.Start();
        }

        private void StartBlurAnimation()
        {
            _animationProgress = 1f;
            _animationTimer.Start();
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            if (_isFocused)
            {
                _animationProgress += 0.1f;
                if (_animationProgress >= 1f)
                {
                    _animationProgress = 1f;
                    _animationTimer.Stop();
                }
            }
            else
            {
                _animationProgress -= 0.1f;
                if (_animationProgress <= 0f)
                {
                    _animationProgress = 0f;
                    _animationTimer.Stop();
                }
            }

            UpdateAnimatedElements();
            Invalidate();
        }

        private void UpdateAnimatedElements()
        {
            // تحديث لون التسمية
            var labelColor = Color.FromArgb(
                (int)(ModernDesignSystem.Colors.TextSecondary.R + 
                      (_accentColor.R - ModernDesignSystem.Colors.TextSecondary.R) * _animationProgress),
                (int)(ModernDesignSystem.Colors.TextSecondary.G + 
                      (_accentColor.G - ModernDesignSystem.Colors.TextSecondary.G) * _animationProgress),
                (int)(ModernDesignSystem.Colors.TextSecondary.B + 
                      (_accentColor.B - ModernDesignSystem.Colors.TextSecondary.B) * _animationProgress)
            );
            _labelText.ForeColor = labelColor;

            // تحديث لون الخط السفلي
            if (_textBoxStyle == TextBoxStyle.Underlined)
            {
                var underlineColor = Color.FromArgb(
                    (int)(ModernDesignSystem.Colors.Border.R + 
                          (_accentColor.R - ModernDesignSystem.Colors.Border.R) * _animationProgress),
                    (int)(ModernDesignSystem.Colors.Border.G + 
                          (_accentColor.G - ModernDesignSystem.Colors.Border.G) * _animationProgress),
                    (int)(ModernDesignSystem.Colors.Border.B + 
                          (_accentColor.B - ModernDesignSystem.Colors.Border.B) * _animationProgress)
                );
                _underline.BackColor = underlineColor;
            }
        }

        #endregion

        #region دوال مساعدة

        private void UpdatePlaceholder()
        {
            // تحديث النص التوضيحي بطريقة محسنة
            if (string.IsNullOrEmpty(_textBox.Text) && !_isFocused && !string.IsNullOrEmpty(_placeholderText))
            {
                // إظهار النص التوضيحي
            }
            else
            {
                // إخفاء النص التوضيحي
            }
        }

        private void UpdateErrorState()
        {
            if (_hasError)
            {
                _helperText.ForeColor = ModernDesignSystem.Colors.Error;
                _labelText.ForeColor = ModernDesignSystem.Colors.Error;
            }
            else
            {
                _helperText.ForeColor = ModernDesignSystem.Colors.TextHint;
                _labelText.ForeColor = ModernDesignSystem.Colors.TextSecondary;
            }
            Invalidate();
        }

        /// <summary>
        /// تركيز المربع
        /// </summary>
        public new void Focus()
        {
            _textBox.Focus();
        }

        /// <summary>
        /// تحديد كامل النص
        /// </summary>
        public void SelectAll()
        {
            _textBox.SelectAll();
        }

        /// <summary>
        /// مسح النص
        /// </summary>
        public void Clear()
        {
            _textBox.Clear();
        }

        #endregion

        #region تنظيف الموارد

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _animationTimer?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }

    /// <summary>
    /// أنماط مربع النص
    /// </summary>
    public enum TextBoxStyle
    {
        Outlined,   // محدد بإطار
        Underlined, // خط سفلي
        Filled      // مملوء
    }
}
