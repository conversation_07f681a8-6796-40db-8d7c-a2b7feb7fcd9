using GashmiDebtManagement.UI.Forms;
using GashmiDebtManagement.Helpers;
using GashmiDebtManagement.Tests;

namespace GashmiDebtManagement
{
    internal static class Program
    {
        /// <summary>
        /// نقطة الدخول الرئيسية للتطبيق
        /// </summary>
        [STAThread]
        static async Task Main(string[] args)
        {
            // فحص معاملات سطر الأوامر
            if (args.Length > 0)
            {
                await HandleCommandLineArgs(args);
                return;
            }

            // إعداد التطبيق للدقة العالية
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.SetHighDpiMode(HighDpiMode.SystemAware);

            try
            {
                // تحميل إعدادات الثيم
                ThemeManager.LoadThemeSettings();

                // تشغيل النافذة الرئيسية المحسنة
                Application.Run(new ModernMainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل التطبيق:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // حفظ إعدادات الثيم عند الإغلاق
                ThemeManager.SaveThemeSettings();
            }
        }

        /// <summary>
        /// معالجة معاملات سطر الأوامر
        /// </summary>
        private static async Task HandleCommandLineArgs(string[] args)
        {
            foreach (var arg in args)
            {
                switch (arg.ToLower())
                {
                    case "--run-ui-tests":
                        Console.OutputEncoding = System.Text.Encoding.UTF8;
                        Console.WriteLine("اختبارات واجهة المستخدم معطلة مؤقتاً");
                        // await ComprehensiveUITests.RunAllUITests();
                        break;

                    case "--setup-db-only":
                        await SetupDatabaseOnly();
                        break;

                    case "--run-rtl-tests":
                        Console.OutputEncoding = System.Text.Encoding.UTF8;
                        RTLTests.RunAllTests();
                        break;

                    case "--help":
                    case "-h":
                        ShowHelp();
                        break;
                }
            }
        }

        /// <summary>
        /// إعداد قاعدة البيانات فقط
        /// </summary>
        private static async Task SetupDatabaseOnly()
        {
            try
            {
                using var context = new Models.ApplicationDbContext();
                context.EnsureCreated();
                Console.WriteLine("✅ تم إعداد قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل في إعداد قاعدة البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// عرض المساعدة
        /// </summary>
        private static void ShowHelp()
        {
            Console.WriteLine("🏢 نظام إدارة ديون الرعية - شركة الغشمي");
            Console.WriteLine("الاستخدام: GashmiDebtManagement.exe [خيارات]");
            Console.WriteLine();
            Console.WriteLine("الخيارات:");
            Console.WriteLine("  --run-ui-tests     تشغيل اختبارات واجهة المستخدم");
            Console.WriteLine("  --setup-db-only    إعداد قاعدة البيانات فقط");
            Console.WriteLine("  --run-rtl-tests    تشغيل اختبارات دعم RTL");
            Console.WriteLine("  --help, -h         عرض هذه المساعدة");
            Console.WriteLine();
            Console.WriteLine("بدون خيارات: تشغيل التطبيق العادي");
        }
    }
}
