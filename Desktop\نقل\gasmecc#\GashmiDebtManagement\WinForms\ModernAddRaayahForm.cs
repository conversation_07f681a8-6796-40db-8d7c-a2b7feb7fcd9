using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Helpers;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج إضافة رعوي جديد - تصميم عصري
    /// </summary>
    public partial class ModernAddRaayahForm : Form
    {
        private readonly IRaayahRepository _raayahRepository;
        
        // عناصر التحكم المحسنة
        private Panel? headerPanel;
        private Panel? contentPanel;
        private Panel? buttonPanel;
        private Label? titleLabel;
        private Label? nameLabel;
        private TextBox? nameTextBox;
        private CheckBox? enableDiscountCheckBox;
        private CheckBox? inKashfOzriCheckBox;
        private CheckBox? inKharijKashfCheckBox;
        private Button? saveButton;
        private Button? cancelButton;
        private Label? validationLabel;

        // الأحداث
        public event EventHandler<RaayahEventArgs>? RaayahAdded;

        public ModernAddRaayahForm(IRaayahRepository raayahRepository)
        {
            _raayahRepository = raayahRepository;
            InitializeComponent();
            InitializeModernDesign();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "إضافة رعوي جديد";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = UIHelper.ModernColors.Background;

            // إنشاء التخطيط
            CreateModernLayout();

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void InitializeModernDesign()
        {
            // تطبيق دعم RTL المحسن
            RTLHelper.ApplyRTL(this);

            // إضافة اختصارات لوحة المفاتيح
            UIHelper.AddKeyboardShortcuts(this);

            // إصلاح تداخل العناصر
            LayerManager.ApplyComprehensiveFix(this);
        }

        private void CreateModernLayout()
        {
            // ترتيب صحيح للطبقات - من الأسفل للأعلى

            // 1. إنشاء الأزرار أولاً (في الأسفل)
            CreateButtonPanel();

            // 2. إنشاء المحتوى (ليملأ المساحة المتبقية)
            CreateContentPanel();

            // 3. إنشاء الرأس أخيراً (في الأعلى)
            CreateHeaderPanel();
        }

        private void CreateHeaderPanel()
        {
            headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = UIHelper.ModernColors.Primary,
                Padding = new Padding(UIHelper.Spacing.Large)
            };

            titleLabel = new Label
            {
                Text = "إضافة رعوي جديد",
                Font = UIHelper.ModernFonts.ArabicHeaderLarge,
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(UIHelper.Spacing.Large, 25)
            };

            headerPanel.Controls.Add(titleLabel);
            this.Controls.Add(headerPanel);
            headerPanel.BringToFront(); // جلب الرأس للمقدمة
        }

        private void CreateContentPanel()
        {
            contentPanel = UIHelper.CreateCardPanel();
            contentPanel.Dock = DockStyle.Fill;
            contentPanel.Padding = new Padding(UIHelper.Spacing.XLarge);

            CreateFormControls();
            this.Controls.Add(contentPanel);
            contentPanel.SendToBack(); // إرسال المحتوى للخلف
        }

        private void CreateFormControls()
        {
            var currentY = UIHelper.Spacing.Medium;

            // تسمية الاسم
            nameLabel = UIHelper.CreateArabicLabel("الاسم الكامل:");
            nameLabel.Location = new Point(UIHelper.Spacing.Large, currentY);
            nameLabel.Size = new Size(120, 25);
            nameLabel.TextAlign = ContentAlignment.MiddleRight;
            currentY += 30;

            // مربع نص الاسم
            nameTextBox = UIHelper.CreateModernTextBox("أدخل الاسم الكامل للرعوي");
            nameTextBox.Location = new Point(UIHelper.Spacing.Large, currentY);
            nameTextBox.Size = new Size(400, UIHelper.Sizes.InputHeight);
            nameTextBox.TextChanged += NameTextBox_TextChanged;
            currentY += 50;

            // مربع اختيار خصم الحوالة
            enableDiscountCheckBox = CreateModernCheckBox(
                "تفعيل خصم الحوالة (3%)",
                "يتم خصم 3% من (إجمالي الديون - المبلغ الواصل)",
                currentY,
                true
            );
            currentY += 50;

            // مربع اختيار كشف الأوزري
            inKashfOzriCheckBox = CreateModernCheckBox(
                "إدراج في كشف الأوزري",
                "سيظهر هذا الرعوي في تقرير كشف الأوزري",
                currentY,
                true
            );
            currentY += 50;

            // مربع اختيار خارج الكشف
            inKharijKashfCheckBox = CreateModernCheckBox(
                "إدراج في خارج الكشف",
                "سيظهر هذا الرعوي في تقرير خارج الكشف",
                currentY,
                false
            );
            currentY += 50;

            // تسمية التحقق
            validationLabel = new Label
            {
                Text = "",
                Font = UIHelper.ModernFonts.Caption,
                ForeColor = UIHelper.ModernColors.Danger,
                Location = new Point(UIHelper.Spacing.Large, currentY),
                Size = new Size(400, 20),
                TextAlign = ContentAlignment.MiddleRight,
                Visible = false
            };

            contentPanel!.Controls.AddRange(new Control[] {
                nameLabel, nameTextBox,
                enableDiscountCheckBox, inKashfOzriCheckBox, inKharijKashfCheckBox,
                validationLabel
            });

            // تعيين التركيز على مربع النص
            nameTextBox.Focus();
        }

        private CheckBox CreateModernCheckBox(string text, string tooltip, int y, bool isChecked)
        {
            var checkBox = new CheckBox
            {
                Text = text,
                Font = UIHelper.ModernFonts.ArabicBody,
                ForeColor = UIHelper.ModernColors.TextPrimary,
                Location = new Point(UIHelper.Spacing.Large, y),
                Size = new Size(400, 25),
                Checked = isChecked,
                UseVisualStyleBackColor = true
            };

            // إضافة tooltip
            var toolTip = new ToolTip();
            toolTip.SetToolTip(checkBox, tooltip);

            return checkBox;
        }

        private void CreateButtonPanel()
        {
            buttonPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Bottom,
                BackColor = UIHelper.ModernColors.Surface,
                Padding = new Padding(UIHelper.Spacing.Large)
            };

            // زر الحفظ
            saveButton = UIHelper.CreateSuccessButton("💾 حفظ", SaveButton_Click);
            saveButton.Size = new Size(120, UIHelper.Sizes.ButtonHeight);
            saveButton.Location = new Point(
                buttonPanel.Width - saveButton.Width - UIHelper.Spacing.Large - 140,
                (buttonPanel.Height - saveButton.Height) / 2
            );
            saveButton.Enabled = false;

            // زر الإلغاء
            cancelButton = UIHelper.CreateSecondaryButton("❌ إلغاء", CancelButton_Click);
            cancelButton.Size = new Size(120, UIHelper.Sizes.ButtonHeight);
            cancelButton.Location = new Point(
                buttonPanel.Width - cancelButton.Width - UIHelper.Spacing.Large,
                (buttonPanel.Height - cancelButton.Height) / 2
            );

            buttonPanel.Controls.AddRange(new Control[] { saveButton, cancelButton });
            this.Controls.Add(buttonPanel);
        }

        #region معالجات الأحداث

        private void NameTextBox_TextChanged(object? sender, EventArgs e)
        {
            var isValid = ValidateForm();
            saveButton!.Enabled = isValid;
        }

        private bool ValidateForm()
        {
            validationLabel!.Visible = false;

            if (string.IsNullOrWhiteSpace(nameTextBox?.Text))
            {
                ShowValidationError("يرجى إدخال اسم الرعوي");
                return false;
            }

            if (nameTextBox.Text.Length < 3)
            {
                ShowValidationError("يجب أن يكون الاسم 3 أحرف على الأقل");
                return false;
            }

            return true;
        }

        private void ShowValidationError(string message)
        {
            validationLabel!.Text = message;
            validationLabel.Visible = true;
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            if (!ValidateForm()) return;

            try
            {
                saveButton!.Enabled = false;
                saveButton.Text = "جاري الحفظ...";

                // التحقق من عدم تكرار الاسم
                var existingRaayah = await _raayahRepository.GetByNameAsync(nameTextBox!.Text.Trim());
                if (existingRaayah != null)
                {
                    ShowValidationError("يوجد رعوي بهذا الاسم مسبقاً");
                    return;
                }

                // إنشاء الرعوي الجديد
                var newRaayah = new Raayah
                {
                    FullName = nameTextBox.Text.Trim(),
                    EnableDiscount = enableDiscountCheckBox!.Checked,
                    InKashfOzri = inKashfOzriCheckBox!.Checked,
                    InKharijKashf = inKharijKashfCheckBox!.Checked,
                    CreatedDate = DateTime.Now
                };

                await _raayahRepository.AddAsync(newRaayah);

                // إثارة الحدث
                RaayahAdded?.Invoke(this, new RaayahEventArgs(newRaayah));

                MessageBox.Show("تم حفظ بيانات الرعوي بنجاح", "نجح الحفظ",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الحفظ:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                saveButton!.Enabled = true;
                saveButton.Text = "💾 حفظ";
            }
        }

        private void CancelButton_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion
    }

    /// <summary>
    /// معاملات حدث الرعية
    /// </summary>
    public class RaayahEventArgs : EventArgs
    {
        public Raayah Raayah { get; }

        public RaayahEventArgs(Raayah raayah)
        {
            Raayah = raayah;
        }
    }
}
