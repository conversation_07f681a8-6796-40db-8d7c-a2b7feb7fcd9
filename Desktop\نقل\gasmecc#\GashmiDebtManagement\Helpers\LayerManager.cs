using System.Windows.Forms;

namespace GashmiDebtManagement.Helpers
{
    /// <summary>
    /// مدير طبقات العناصر لمنع تداخل العناصر المرئية
    /// </summary>
    public static class LayerManager
    {
        /// <summary>
        /// ترتيب طبقات النافذة بشكل صحيح
        /// </summary>
        public static void ArrangeFormLayers(Form form)
        {
            if (form == null) return;

            // البحث عن العناصر الأساسية
            Panel? headerPanel = null;
            Panel? toolbarPanel = null;
            Panel? contentPanel = null;
            Panel? statusPanel = null;
            Panel? buttonPanel = null;
            Panel? sidebarPanel = null;

            // تحديد العناصر
            foreach (Control control in form.Controls)
            {
                if (control is Panel panel)
                {
                    switch (panel.Dock)
                    {
                        case DockStyle.Top:
                            if (panel.Height <= 100 && panel.BackColor == UIHelper.ModernColors.Primary)
                                headerPanel = panel;
                            else if (panel.Height <= 60)
                                toolbarPanel = panel;
                            break;
                        case DockStyle.Bottom:
                            if (panel.Height <= 50)
                                statusPanel = panel;
                            else
                                buttonPanel = panel;
                            break;
                        case DockStyle.Fill:
                            contentPanel = panel;
                            break;
                        case DockStyle.Left:
                        case DockStyle.Right:
                            sidebarPanel = panel;
                            break;
                    }
                }
            }

            // ترتيب الطبقات بالترتيب الصحيح
            ArrangeLayers(contentPanel, sidebarPanel, statusPanel, buttonPanel, toolbarPanel, headerPanel);
        }

        /// <summary>
        /// ترتيب طبقات العناصر بالترتيب الصحيح
        /// </summary>
        private static void ArrangeLayers(params Panel?[] panels)
        {
            foreach (var panel in panels)
            {
                if (panel != null)
                {
                    // إرسال المحتوى للخلف أولاً
                    if (panel.Dock == DockStyle.Fill)
                    {
                        panel.SendToBack();
                    }
                    // جلب العناصر الأخرى للمقدمة
                    else
                    {
                        panel.BringToFront();
                    }
                }
            }
        }

        /// <summary>
        /// إصلاح تداخل العناصر في النافذة
        /// </summary>
        public static void FixOverlappingControls(Form form)
        {
            if (form == null) return;

            // ترتيب طبقات النافذة
            ArrangeFormLayers(form);

            // إصلاح مشاكل التداخل الشائعة
            FixCommonOverlapIssues(form);

            // تطبيق هوامش آمنة
            ApplySafeMargins(form);
        }

        /// <summary>
        /// إصلاح مشاكل التداخل الشائعة
        /// </summary>
        private static void FixCommonOverlapIssues(Form form)
        {
            foreach (Control control in form.Controls)
            {
                if (control is Panel panel)
                {
                    switch (panel.Dock)
                    {
                        case DockStyle.Top:
                            // التأكد من أن العناصر العلوية لا تتداخل
                            EnsureTopPanelVisibility(panel);
                            break;
                        case DockStyle.Fill:
                            // التأكد من أن المحتوى لا يختفي خلف العناصر الأخرى
                            EnsureContentVisibility(panel, form);
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// التأكد من ظهور اللوحات العلوية
        /// </summary>
        private static void EnsureTopPanelVisibility(Panel panel)
        {
            panel.BringToFront();
            
            // إضافة حد سفلي للتمييز
            if (!HasBottomBorder(panel))
            {
                AddBottomBorder(panel);
            }
        }

        /// <summary>
        /// التأكد من ظهور المحتوى
        /// </summary>
        private static void EnsureContentVisibility(Panel contentPanel, Form form)
        {
            contentPanel.SendToBack();

            // حساب المساحة المطلوبة للعناصر الأخرى
            var topSpace = CalculateTopSpace(form);
            var bottomSpace = CalculateBottomSpace(form);

            // تطبيق هوامش آمنة
            if (contentPanel.Padding.Top < topSpace)
            {
                contentPanel.Padding = new Padding(
                    contentPanel.Padding.Left,
                    Math.Max(contentPanel.Padding.Top, topSpace),
                    contentPanel.Padding.Right,
                    Math.Max(contentPanel.Padding.Bottom, bottomSpace)
                );
            }
        }

        /// <summary>
        /// حساب المساحة المطلوبة في الأعلى
        /// </summary>
        private static int CalculateTopSpace(Form form)
        {
            var topSpace = 0;
            foreach (Control control in form.Controls)
            {
                if (control is Panel panel && panel.Dock == DockStyle.Top)
                {
                    topSpace += panel.Height;
                }
            }
            return topSpace;
        }

        /// <summary>
        /// حساب المساحة المطلوبة في الأسفل
        /// </summary>
        private static int CalculateBottomSpace(Form form)
        {
            var bottomSpace = 0;
            foreach (Control control in form.Controls)
            {
                if (control is Panel panel && panel.Dock == DockStyle.Bottom)
                {
                    bottomSpace += panel.Height;
                }
            }
            return bottomSpace;
        }

        /// <summary>
        /// التحقق من وجود حد سفلي
        /// </summary>
        private static bool HasBottomBorder(Panel panel)
        {
            // فحص بسيط للحد السفلي
            return panel.Controls.OfType<Panel>().Any(p => p.Dock == DockStyle.Bottom && p.Height <= 5);
        }

        /// <summary>
        /// إضافة حد سفلي
        /// </summary>
        private static void AddBottomBorder(Panel panel)
        {
            var border = new Panel
            {
                Height = 2,
                Dock = DockStyle.Bottom,
                BackColor = UIHelper.ModernColors.Border
            };
            panel.Controls.Add(border);
        }

        /// <summary>
        /// تطبيق هوامش آمنة
        /// </summary>
        private static void ApplySafeMargins(Form form)
        {
            foreach (Control control in form.Controls)
            {
                if (control is Panel panel && panel.Dock == DockStyle.Fill)
                {
                    // تطبيق هوامش آمنة للمحتوى
                    var safeMargin = UIHelper.Spacing.Medium;
                    if (panel.Padding.All < safeMargin)
                    {
                        panel.Padding = new Padding(safeMargin);
                    }
                }
            }
        }

        /// <summary>
        /// إصلاح تداخل الجداول
        /// </summary>
        public static void FixDataGridViewOverlap(DataGridView dataGridView)
        {
            if (dataGridView?.Parent == null) return;

            var parent = dataGridView.Parent;
            
            // التأكد من أن الجدول في الخلف
            dataGridView.SendToBack();

            // إضافة هوامش آمنة
            if (dataGridView.Dock == DockStyle.Fill)
            {
                parent.Padding = new Padding(
                    Math.Max(parent.Padding.Left, UIHelper.Spacing.Medium),
                    Math.Max(parent.Padding.Top, UIHelper.Spacing.Medium),
                    Math.Max(parent.Padding.Right, UIHelper.Spacing.Medium),
                    Math.Max(parent.Padding.Bottom, UIHelper.Spacing.Medium)
                );
            }
        }

        /// <summary>
        /// إصلاح تداخل الأزرار
        /// </summary>
        public static void FixButtonOverlap(Panel buttonPanel)
        {
            if (buttonPanel == null) return;

            // جلب لوحة الأزرار للمقدمة
            buttonPanel.BringToFront();

            // إضافة حد علوي للتمييز
            if (!HasTopBorder(buttonPanel))
            {
                AddTopBorder(buttonPanel);
            }
        }

        /// <summary>
        /// التحقق من وجود حد علوي
        /// </summary>
        private static bool HasTopBorder(Panel panel)
        {
            return panel.Controls.OfType<Panel>().Any(p => p.Dock == DockStyle.Top && p.Height <= 5);
        }

        /// <summary>
        /// إضافة حد علوي
        /// </summary>
        private static void AddTopBorder(Panel panel)
        {
            var border = new Panel
            {
                Height = 1,
                Dock = DockStyle.Top,
                BackColor = UIHelper.ModernColors.Border
            };
            panel.Controls.Add(border);
            border.BringToFront();
        }

        /// <summary>
        /// تطبيق إصلاحات شاملة على النافذة
        /// </summary>
        public static void ApplyComprehensiveFix(Form form)
        {
            if (form == null) return;

            // إصلاح تداخل العناصر
            FixOverlappingControls(form);

            // إصلاح الجداول
            foreach (Control control in GetAllControls(form))
            {
                if (control is DataGridView dgv)
                {
                    FixDataGridViewOverlap(dgv);
                }
                else if (control is Panel panel && panel.Dock == DockStyle.Bottom)
                {
                    FixButtonOverlap(panel);
                }
            }

            // تحديث العرض
            form.Refresh();
        }

        /// <summary>
        /// الحصول على جميع العناصر في النافذة
        /// </summary>
        private static IEnumerable<Control> GetAllControls(Control container)
        {
            foreach (Control control in container.Controls)
            {
                yield return control;
                foreach (Control child in GetAllControls(control))
                {
                    yield return child;
                }
            }
        }
    }
}
