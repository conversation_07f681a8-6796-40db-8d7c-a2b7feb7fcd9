using System.Drawing;
using System.Windows.Forms;

namespace GashmiDebtManagement.Helpers
{
    /// <summary>
    /// مدير الثيمات والألوان
    /// </summary>
    public static class ThemeManager
    {
        #region الثيمات المتاحة

        public enum ThemeType
        {
            Light,      // فاتح
            Dark,       // داكن
            Blue,       // أزرق
            Green,      // أخضر
            Purple      // بنفسجي
        }

        private static ThemeType _currentTheme = ThemeType.Light;

        public static ThemeType CurrentTheme
        {
            get => _currentTheme;
            set
            {
                _currentTheme = value;
                OnThemeChanged?.Invoke();
            }
        }

        public static event Action? OnThemeChanged;

        #endregion

        #region ألوان الثيمات

        public static class LightTheme
        {
            public static readonly Color Primary = Color.FromArgb(41, 128, 185);
            public static readonly Color Secondary = Color.FromArgb(52, 152, 219);
            public static readonly Color Success = Color.FromArgb(39, 174, 96);
            public static readonly Color Warning = Color.FromArgb(241, 196, 15);
            public static readonly Color Danger = Color.FromArgb(231, 76, 60);
            public static readonly Color Info = Color.FromArgb(142, 68, 173);
            
            public static readonly Color Background = Color.FromArgb(248, 249, 250);
            public static readonly Color Surface = Color.White;
            public static readonly Color Card = Color.White;
            
            public static readonly Color TextPrimary = Color.FromArgb(33, 37, 41);
            public static readonly Color TextSecondary = Color.FromArgb(108, 117, 125);
            public static readonly Color TextMuted = Color.FromArgb(173, 181, 189);
            
            public static readonly Color Border = Color.FromArgb(222, 226, 230);
            public static readonly Color BorderDark = Color.FromArgb(173, 181, 189);
            
            public static readonly Color Hover = Color.FromArgb(240, 242, 245);
            public static readonly Color Active = Color.FromArgb(233, 236, 239);
        }

        public static class DarkTheme
        {
            public static readonly Color Primary = Color.FromArgb(52, 152, 219);
            public static readonly Color Secondary = Color.FromArgb(74, 144, 226);
            public static readonly Color Success = Color.FromArgb(46, 204, 113);
            public static readonly Color Warning = Color.FromArgb(241, 196, 15);
            public static readonly Color Danger = Color.FromArgb(231, 76, 60);
            public static readonly Color Info = Color.FromArgb(155, 89, 182);
            
            public static readonly Color Background = Color.FromArgb(33, 37, 41);
            public static readonly Color Surface = Color.FromArgb(52, 58, 64);
            public static readonly Color Card = Color.FromArgb(73, 80, 87);
            
            public static readonly Color TextPrimary = Color.FromArgb(248, 249, 250);
            public static readonly Color TextSecondary = Color.FromArgb(173, 181, 189);
            public static readonly Color TextMuted = Color.FromArgb(108, 117, 125);
            
            public static readonly Color Border = Color.FromArgb(73, 80, 87);
            public static readonly Color BorderDark = Color.FromArgb(52, 58, 64);
            
            public static readonly Color Hover = Color.FromArgb(73, 80, 87);
            public static readonly Color Active = Color.FromArgb(108, 117, 125);
        }

        public static class BlueTheme
        {
            public static readonly Color Primary = Color.FromArgb(0, 123, 255);
            public static readonly Color Secondary = Color.FromArgb(108, 117, 125);
            public static readonly Color Success = Color.FromArgb(40, 167, 69);
            public static readonly Color Warning = Color.FromArgb(255, 193, 7);
            public static readonly Color Danger = Color.FromArgb(220, 53, 69);
            public static readonly Color Info = Color.FromArgb(23, 162, 184);
            
            public static readonly Color Background = Color.FromArgb(240, 248, 255);
            public static readonly Color Surface = Color.White;
            public static readonly Color Card = Color.White;
            
            public static readonly Color TextPrimary = Color.FromArgb(33, 37, 41);
            public static readonly Color TextSecondary = Color.FromArgb(108, 117, 125);
            public static readonly Color TextMuted = Color.FromArgb(173, 181, 189);
            
            public static readonly Color Border = Color.FromArgb(176, 216, 255);
            public static readonly Color BorderDark = Color.FromArgb(108, 117, 125);
            
            public static readonly Color Hover = Color.FromArgb(230, 244, 255);
            public static readonly Color Active = Color.FromArgb(204, 229, 255);
        }

        #endregion

        #region الحصول على ألوان الثيم الحالي

        public static Color GetPrimaryColor()
        {
            return CurrentTheme switch
            {
                ThemeType.Light => LightTheme.Primary,
                ThemeType.Dark => DarkTheme.Primary,
                ThemeType.Blue => BlueTheme.Primary,
                _ => LightTheme.Primary
            };
        }

        public static Color GetBackgroundColor()
        {
            return CurrentTheme switch
            {
                ThemeType.Light => LightTheme.Background,
                ThemeType.Dark => DarkTheme.Background,
                ThemeType.Blue => BlueTheme.Background,
                _ => LightTheme.Background
            };
        }

        public static Color GetSurfaceColor()
        {
            return CurrentTheme switch
            {
                ThemeType.Light => LightTheme.Surface,
                ThemeType.Dark => DarkTheme.Surface,
                ThemeType.Blue => BlueTheme.Surface,
                _ => LightTheme.Surface
            };
        }

        public static Color GetTextPrimaryColor()
        {
            return CurrentTheme switch
            {
                ThemeType.Light => LightTheme.TextPrimary,
                ThemeType.Dark => DarkTheme.TextPrimary,
                ThemeType.Blue => BlueTheme.TextPrimary,
                _ => LightTheme.TextPrimary
            };
        }

        public static Color GetBorderColor()
        {
            return CurrentTheme switch
            {
                ThemeType.Light => LightTheme.Border,
                ThemeType.Dark => DarkTheme.Border,
                ThemeType.Blue => BlueTheme.Border,
                _ => LightTheme.Border
            };
        }

        #endregion

        #region تطبيق الثيم على النوافذ

        /// <summary>
        /// تطبيق الثيم على النموذج
        /// </summary>
        public static void ApplyTheme(Form form)
        {
            form.BackColor = GetBackgroundColor();
            form.ForeColor = GetTextPrimaryColor();

            ApplyThemeToControls(form);
        }

        /// <summary>
        /// تطبيق الثيم على جميع العناصر
        /// </summary>
        private static void ApplyThemeToControls(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                switch (control)
                {
                    case Panel panel:
                        ApplyThemeToPanel(panel);
                        break;
                    case Button button:
                        ApplyThemeToButton(button);
                        break;
                    case TextBox textBox:
                        ApplyThemeToTextBox(textBox);
                        break;
                    case Label label:
                        ApplyThemeToLabel(label);
                        break;
                    case DataGridView dataGridView:
                        ApplyThemeToDataGridView(dataGridView);
                        break;
                }

                // تطبيق على العناصر الفرعية
                if (control.HasChildren)
                {
                    ApplyThemeToControls(control);
                }
            }
        }

        private static void ApplyThemeToPanel(Panel panel)
        {
            if (panel.BackColor == SystemColors.Control || panel.BackColor == Color.White)
            {
                panel.BackColor = GetSurfaceColor();
            }
            panel.ForeColor = GetTextPrimaryColor();
        }

        private static void ApplyThemeToButton(Button button)
        {
            if (button.BackColor == SystemColors.Control)
            {
                button.BackColor = GetPrimaryColor();
                button.ForeColor = Color.White;
            }
        }

        private static void ApplyThemeToTextBox(TextBox textBox)
        {
            textBox.BackColor = GetSurfaceColor();
            textBox.ForeColor = GetTextPrimaryColor();
        }

        private static void ApplyThemeToLabel(Label label)
        {
            if (label.BackColor == SystemColors.Control || label.BackColor == Color.Transparent)
            {
                label.BackColor = Color.Transparent;
            }
            label.ForeColor = GetTextPrimaryColor();
        }

        private static void ApplyThemeToDataGridView(DataGridView dataGridView)
        {
            dataGridView.BackgroundColor = GetBackgroundColor();
            dataGridView.DefaultCellStyle.BackColor = GetSurfaceColor();
            dataGridView.DefaultCellStyle.ForeColor = GetTextPrimaryColor();
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = GetPrimaryColor();
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
        }

        #endregion

        #region حفظ واستعادة الإعدادات

        /// <summary>
        /// حفظ إعدادات الثيم
        /// </summary>
        public static void SaveThemeSettings()
        {
            try
            {
                var settingsPath = Path.Combine(Application.StartupPath, "theme.settings");
                File.WriteAllText(settingsPath, CurrentTheme.ToString());
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// استعادة إعدادات الثيم
        /// </summary>
        public static void LoadThemeSettings()
        {
            try
            {
                var settingsPath = Path.Combine(Application.StartupPath, "theme.settings");
                if (File.Exists(settingsPath))
                {
                    var themeText = File.ReadAllText(settingsPath);
                    if (Enum.TryParse<ThemeType>(themeText, out var theme))
                    {
                        CurrentTheme = theme;
                    }
                }
            }
            catch
            {
                // استخدام الثيم الافتراضي
                CurrentTheme = ThemeType.Light;
            }
        }

        #endregion
    }
}
