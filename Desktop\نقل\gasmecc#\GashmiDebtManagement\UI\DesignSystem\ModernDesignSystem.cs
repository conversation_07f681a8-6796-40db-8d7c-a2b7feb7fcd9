using System.Drawing;
using System.Drawing.Drawing2D;

namespace GashmiDebtManagement.UI.DesignSystem
{
    /// <summary>
    /// نظام التصميم المتقدم - Material Design مع دعم العربية
    /// </summary>
    public static class ModernDesignSystem
    {
        #region نظام الألوان المتقدم

        /// <summary>
        /// لوحة الألوان الأساسية - مستوحاة من Material Design
        /// </summary>
        public static class Colors
        {
            // الألوان الأساسية
            public static readonly Color Primary = Color.FromArgb(33, 150, 243);      // أزرق حديث
            public static readonly Color PrimaryDark = Color.FromArgb(21, 101, 192);  // أزرق داكن
            public static readonly Color PrimaryLight = Color.FromArgb(144, 202, 249); // أزرق فاتح
            public static readonly Color Accent = Color.FromArgb(255, 193, 7);        // ذهبي للتمييز

            // ألوان الخلفية
            public static readonly Color Background = Color.FromArgb(250, 250, 250);   // خلفية فاتحة
            public static readonly Color Surface = Color.White;                       // سطح أبيض
            public static readonly Color Card = Color.FromArgb(255, 255, 255);       // بطاقات
            public static readonly Color Elevated = Color.FromArgb(248, 249, 250);   // عناصر مرفوعة

            // ألوان النصوص
            public static readonly Color TextPrimary = Color.FromArgb(33, 33, 33);    // نص أساسي
            public static readonly Color TextSecondary = Color.FromArgb(117, 117, 117); // نص ثانوي
            public static readonly Color TextHint = Color.FromArgb(158, 158, 158);    // نص تلميح
            public static readonly Color TextOnPrimary = Color.White;                 // نص على الأساسي

            // ألوان الحالة
            public static readonly Color Success = Color.FromArgb(76, 175, 80);       // نجاح
            public static readonly Color Warning = Color.FromArgb(255, 152, 0);       // تحذير
            public static readonly Color Error = Color.FromArgb(244, 67, 54);         // خطأ
            public static readonly Color Info = Color.FromArgb(33, 150, 243);         // معلومات

            // ألوان الحدود والفواصل
            public static readonly Color Border = Color.FromArgb(224, 224, 224);      // حدود
            public static readonly Color Divider = Color.FromArgb(238, 238, 238);     // فواصل
            public static readonly Color Outline = Color.FromArgb(189, 189, 189);     // خطوط خارجية

            // ألوان التفاعل
            public static readonly Color Hover = Color.FromArgb(245, 245, 245);       // تمرير الماوس
            public static readonly Color Pressed = Color.FromArgb(238, 238, 238);     // ضغط
            public static readonly Color Selected = Color.FromArgb(227, 242, 253);    // تحديد
            public static readonly Color Focus = Color.FromArgb(33, 150, 243);        // تركيز

            // تدرجات لونية
            public static LinearGradientBrush PrimaryGradient => new LinearGradientBrush(
                new Point(0, 0), new Point(0, 100),
                Primary, PrimaryDark);

            public static LinearGradientBrush AccentGradient => new LinearGradientBrush(
                new Point(0, 0), new Point(100, 0),
                Accent, Color.FromArgb(255, 235, 59));
        }

        #endregion

        #region نظام الخطوط العربية المتقدم

        /// <summary>
        /// نظام الخطوط المحسن للعربية
        /// </summary>
        public static class Typography
        {
            // أسماء الخطوط العربية المفضلة
            private static readonly string[] ArabicFonts = {
                "Cairo",           // خط حديث وواضح
                "Tajawal",         // خط أنيق للواجهات
                "Amiri",           // خط تقليدي جميل
                "Noto Sans Arabic", // خط Google المتميز
                "Segoe UI",        // احتياطي
                "Tahoma"           // احتياطي أخير
            };

            // الحصول على أفضل خط متاح
            private static string GetBestArabicFont()
            {
                foreach (var fontName in ArabicFonts)
                {
                    try
                    {
                        using var font = new Font(fontName, 12);
                        if (font.Name == fontName)
                            return fontName;
                    }
                    catch { }
                }
                return "Tahoma"; // الافتراضي
            }

            private static readonly string BestFont = GetBestArabicFont();

            // أحجام الخطوط
            public static class Sizes
            {
                public const float Display = 32f;      // عناوين كبيرة
                public const float Headline = 24f;     // عناوين رئيسية
                public const float Title = 20f;        // عناوين فرعية
                public const float Subheading = 16f;   // عناوين صغيرة
                public const float Body = 14f;         // نص أساسي
                public const float Caption = 12f;      // نص صغير
                public const float Overline = 10f;     // نص فوقي
            }

            // الخطوط المعرفة مسبقاً
            public static readonly Font Display = new Font(BestFont, Sizes.Display, FontStyle.Bold);
            public static readonly Font Headline = new Font(BestFont, Sizes.Headline, FontStyle.Bold);
            public static readonly Font Title = new Font(BestFont, Sizes.Title, FontStyle.Bold);
            public static readonly Font Subheading = new Font(BestFont, Sizes.Subheading, FontStyle.Regular);
            public static readonly Font Body = new Font(BestFont, Sizes.Body, FontStyle.Regular);
            public static readonly Font Caption = new Font(BestFont, Sizes.Caption, FontStyle.Regular);
            public static readonly Font Overline = new Font(BestFont, Sizes.Overline, FontStyle.Regular);

            // خطوط خاصة
            public static readonly Font ButtonText = new Font(BestFont, Sizes.Body, FontStyle.Bold);
            public static readonly Font InputText = new Font(BestFont, Sizes.Body, FontStyle.Regular);
        }

        #endregion

        #region نظام المسافات والأبعاد

        /// <summary>
        /// نظام المسافات المتدرج
        /// </summary>
        public static class Spacing
        {
            public const int XSmall = 4;
            public const int Small = 8;
            public const int Medium = 16;
            public const int Large = 24;
            public const int XLarge = 32;
            public const int XXLarge = 48;
            public const int XXXLarge = 64;
        }

        /// <summary>
        /// أبعاد العناصر المعيارية
        /// </summary>
        public static class Dimensions
        {
            // ارتفاعات العناصر
            public const int ButtonHeight = 40;
            public const int InputHeight = 40;
            public const int ToolbarHeight = 56;
            public const int HeaderHeight = 64;
            public const int StatusBarHeight = 24;
            public const int SidebarWidth = 280;

            // أحجام الأيقونات
            public const int IconSmall = 16;
            public const int IconMedium = 24;
            public const int IconLarge = 32;

            // نصف أقطار الحواف
            public const int BorderRadiusSmall = 4;
            public const int BorderRadiusMedium = 8;
            public const int BorderRadiusLarge = 12;
            public const int BorderRadiusXLarge = 16;
        }

        #endregion

        #region نظام الظلال والتأثيرات

        /// <summary>
        /// نظام الظلال المتدرج
        /// </summary>
        public static class Shadows
        {
            // مستويات الارتفاع
            public static readonly Color Shadow1 = Color.FromArgb(20, 0, 0, 0);   // ظل خفيف
            public static readonly Color Shadow2 = Color.FromArgb(30, 0, 0, 0);   // ظل متوسط
            public static readonly Color Shadow3 = Color.FromArgb(40, 0, 0, 0);   // ظل قوي
            public static readonly Color Shadow4 = Color.FromArgb(50, 0, 0, 0);   // ظل قوي جداً

            // إنشاء ظل مخصص
            public static void DrawShadow(Graphics g, Rectangle rect, int elevation = 1)
            {
                var shadowColor = elevation switch
                {
                    1 => Shadow1,
                    2 => Shadow2,
                    3 => Shadow3,
                    _ => Shadow4
                };

                var shadowRect = new Rectangle(rect.X + elevation, rect.Y + elevation, rect.Width, rect.Height);
                using var brush = new SolidBrush(shadowColor);
                g.FillRoundedRectangle(brush, shadowRect, Dimensions.BorderRadiusMedium);
            }
        }

        #endregion

        #region نظام الأيقونات

        /// <summary>
        /// مجموعة الأيقونات المعيارية
        /// </summary>
        public static class Icons
        {
            // أيقونات التنقل
            public const string Home = "🏠";
            public const string Dashboard = "📊";
            public const string People = "👥";
            public const string Calendar = "📅";
            public const string Reports = "📈";
            public const string Settings = "⚙️";
            public const string Tools = "🔧";

            // أيقونات العمليات
            public const string Add = "➕";
            public const string Edit = "✏️";
            public const string Delete = "🗑️";
            public const string Save = "💾";
            public const string Cancel = "❌";
            public const string Search = "🔍";
            public const string Filter = "🔽";
            public const string Refresh = "🔄";

            // أيقونات الحالة
            public const string Success = "✅";
            public const string Warning = "⚠️";
            public const string Error = "❌";
            public const string Info = "ℹ️";

            // أيقونات الملفات
            public const string Export = "📤";
            public const string Import = "📥";
            public const string Print = "🖨️";
            public const string Download = "⬇️";
        }

        #endregion

        #region دوال مساعدة للرسم

        /// <summary>
        /// رسم مستطيل بحواف مدورة
        /// </summary>
        public static GraphicsPath CreateRoundedRectangle(Rectangle rect, int radius)
        {
            var path = new GraphicsPath();
            var diameter = radius * 2;

            path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90);
            path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90);
            path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
            path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90);
            path.CloseFigure();

            return path;
        }

        /// <summary>
        /// ملء مستطيل بحواف مدورة
        /// </summary>
        public static void FillRoundedRectangle(this Graphics g, Brush brush, Rectangle rect, int radius)
        {
            using var path = CreateRoundedRectangle(rect, radius);
            g.FillPath(brush, path);
        }

        /// <summary>
        /// رسم حدود مستطيل بحواف مدورة
        /// </summary>
        public static void DrawRoundedRectangle(this Graphics g, Pen pen, Rectangle rect, int radius)
        {
            using var path = CreateRoundedRectangle(rect, radius);
            g.DrawPath(pen, path);
        }

        #endregion
    }
}
