using System.Windows.Forms;
using GashmiDebtManagement.Helpers;

namespace GashmiDebtManagement.Tests
{
    /// <summary>
    /// اختبارات دعم اللغة العربية والاتجاه من اليمين لليسار
    /// </summary>
    public static class RTLTests
    {
        /// <summary>
        /// تشغيل جميع اختبارات RTL
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== اختبارات دعم اللغة العربية (RTL) ===");
            Console.WriteLine();

            TestArabicHelper();
            TestRTLHelper();
            TestUIHelper();
            TestFormRTL();

            Console.WriteLine();
            Console.WriteLine("=== انتهت اختبارات RTL ===");
        }

        /// <summary>
        /// اختبار ArabicHelper
        /// </summary>
        private static void TestArabicHelper()
        {
            Console.WriteLine("🔤 اختبار ArabicHelper:");

            // اختبار تحويل الأرقام
            var englishNumbers = "12345";
            var arabicNumbers = ArabicHelper.ConvertToArabicNumbers(englishNumbers);
            Console.WriteLine($"   الأرقام الإنجليزية: {englishNumbers}");
            Console.WriteLine($"   الأرقام العربية: {arabicNumbers}");

            var backToEnglish = ArabicHelper.ConvertToEnglishNumbers(arabicNumbers);
            Console.WriteLine($"   العودة للإنجليزية: {backToEnglish}");

            // اختبار تنسيق التاريخ
            var testDate = DateTime.Now;
            var arabicDate = ArabicHelper.FormatArabicDate(testDate);
            var arabicDateTime = ArabicHelper.FormatArabicDateTime(testDate);
            Console.WriteLine($"   التاريخ العربي: {arabicDate}");
            Console.WriteLine($"   التاريخ والوقت العربي: {arabicDateTime}");

            // اختبار تنسيق المبلغ
            var amount = 1234.56m;
            var currency = ArabicHelper.FormatCurrency(amount);
            var currencyWithArabicNumbers = ArabicHelper.ConvertToArabicNumbers(currency);
            Console.WriteLine($"   المبلغ: {currency}");
            Console.WriteLine($"   المبلغ بالأرقام العربية: {currencyWithArabicNumbers}");

            // اختبار أسماء الأيام والأشهر
            var dayName = ArabicHelper.GetArabicDayName(testDate.DayOfWeek);
            var monthName = ArabicHelper.GetArabicMonthName(testDate.Month);
            Console.WriteLine($"   اسم اليوم: {dayName}");
            Console.WriteLine($"   اسم الشهر: {monthName}");

            Console.WriteLine("   ✅ نجح اختبار ArabicHelper");
            Console.WriteLine();
        }

        /// <summary>
        /// اختبار RTLHelper
        /// </summary>
        private static void TestRTLHelper()
        {
            Console.WriteLine("🔄 اختبار RTLHelper:");

            // اختبار تحديد المحاذاة العربية
            var leftAlign = ContentAlignment.MiddleLeft;
            var arabicAlign = RTLHelper.GetArabicAlignment(leftAlign);
            Console.WriteLine($"   المحاذاة الأصلية: {leftAlign}");
            Console.WriteLine($"   المحاذاة العربية: {arabicAlign}");

            // اختبار المحاذاة الأفقية
            var leftHorizontal = HorizontalAlignment.Left;
            var arabicHorizontal = RTLHelper.GetArabicHorizontalAlignment(leftHorizontal);
            Console.WriteLine($"   المحاذاة الأفقية الأصلية: {leftHorizontal}");
            Console.WriteLine($"   المحاذاة الأفقية العربية: {arabicHorizontal}");

            // اختبار نقطة الإرساء
            var leftAnchor = AnchorStyles.Top | AnchorStyles.Left;
            var arabicAnchor = RTLHelper.GetArabicAnchor(leftAnchor);
            Console.WriteLine($"   الإرساء الأصلي: {leftAnchor}");
            Console.WriteLine($"   الإرساء العربي: {arabicAnchor}");

            // اختبار موقع RTL
            var originalLocation = new Point(100, 50);
            var containerWidth = 800;
            var elementWidth = 200;
            var rtlLocation = RTLHelper.GetRTLLocation(originalLocation, containerWidth, elementWidth);
            Console.WriteLine($"   الموقع الأصلي: {originalLocation}");
            Console.WriteLine($"   موقع RTL: {rtlLocation}");

            Console.WriteLine("   ✅ نجح اختبار RTLHelper");
            Console.WriteLine();
        }

        /// <summary>
        /// اختبار UIHelper
        /// </summary>
        private static void TestUIHelper()
        {
            Console.WriteLine("🎨 اختبار UIHelper:");

            // اختبار إنشاء عناصر عربية
            try
            {
                var arabicLabel = UIHelper.CreateArabicLabel("تسمية عربية");
                Console.WriteLine($"   تم إنشاء تسمية عربية: {arabicLabel.Text}");
                Console.WriteLine($"   اتجاه النص: {arabicLabel.RightToLeft}");
                Console.WriteLine($"   محاذاة النص: {arabicLabel.TextAlign}");

                var arabicTextBox = UIHelper.CreateModernTextBox("نص تجريبي");
                Console.WriteLine($"   تم إنشاء مربع نص عربي");
                Console.WriteLine($"   اتجاه النص: {arabicTextBox.RightToLeft}");
                Console.WriteLine($"   محاذاة النص: {arabicTextBox.TextAlign}");

                var arabicButton = UIHelper.CreateArabicButton("زر عربي", 
                    UIHelper.ModernColors.Primary, Color.White);
                Console.WriteLine($"   تم إنشاء زر عربي: {arabicButton.Text}");
                Console.WriteLine($"   اتجاه النص: {arabicButton.RightToLeft}");

                Console.WriteLine("   ✅ نجح اختبار UIHelper");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ فشل اختبار UIHelper: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// اختبار تطبيق RTL على النموذج
        /// </summary>
        private static void TestFormRTL()
        {
            Console.WriteLine("📋 اختبار تطبيق RTL على النموذج:");

            try
            {
                using var testForm = new Form
                {
                    Text = "نموذج اختبار RTL",
                    Size = new Size(400, 300),
                    StartPosition = FormStartPosition.CenterScreen
                };

                // إضافة عناصر تحكم للاختبار
                var label = new Label
                {
                    Text = "تسمية تجريبية",
                    Location = new Point(20, 20),
                    AutoSize = true
                };

                var textBox = new TextBox
                {
                    Text = "مربع نص تجريبي",
                    Location = new Point(20, 50),
                    Width = 200
                };

                var button = new Button
                {
                    Text = "زر تجريبي",
                    Location = new Point(20, 80),
                    Size = new Size(100, 30)
                };

                testForm.Controls.AddRange(new Control[] { label, textBox, button });

                // تطبيق RTL
                RTLHelper.ApplyRTL(testForm);

                Console.WriteLine($"   اتجاه النموذج: {testForm.RightToLeft}");
                Console.WriteLine($"   تخطيط RTL: {testForm.RightToLeftLayout}");
                Console.WriteLine($"   اتجاه التسمية: {label.RightToLeft}");
                Console.WriteLine($"   محاذاة مربع النص: {textBox.TextAlign}");
                Console.WriteLine($"   اتجاه الزر: {button.RightToLeft}");

                Console.WriteLine("   ✅ نجح اختبار تطبيق RTL على النموذج");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ فشل اختبار النموذج: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// اختبار شامل للواجهات المحسنة
        /// </summary>
        public static void TestModernForms()
        {
            Console.WriteLine("🖥️ اختبار الواجهات المحسنة:");

            try
            {
                // اختبار النافذة الرئيسية المحسنة
                Console.WriteLine("   اختبار ModernMainForm...");
                // يمكن إضافة اختبارات محددة هنا

                Console.WriteLine("   ✅ نجحت اختبارات الواجهات المحسنة");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ فشلت اختبارات الواجهات: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// اختبار الخطوط العربية
        /// </summary>
        public static void TestArabicFonts()
        {
            Console.WriteLine("🔤 اختبار الخطوط العربية:");

            try
            {
                var fonts = new[]
                {
                    UIHelper.ModernFonts.ArabicHeaderLarge,
                    UIHelper.ModernFonts.ArabicHeaderMedium,
                    UIHelper.ModernFonts.ArabicBody,
                    UIHelper.ModernFonts.ArabicCaption
                };

                foreach (var font in fonts)
                {
                    Console.WriteLine($"   خط: {font.Name}, الحجم: {font.Size}, النمط: {font.Style}");
                }

                Console.WriteLine("   ✅ نجح اختبار الخطوط العربية");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ فشل اختبار الخطوط: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// اختبار الألوان والثيمات
        /// </summary>
        public static void TestThemes()
        {
            Console.WriteLine("🎨 اختبار الثيمات:");

            try
            {
                var themes = new[]
                {
                    ThemeManager.ThemeType.Light,
                    ThemeManager.ThemeType.Dark,
                    ThemeManager.ThemeType.Blue
                };

                foreach (var theme in themes)
                {
                    ThemeManager.CurrentTheme = theme;
                    var primaryColor = ThemeManager.GetPrimaryColor();
                    var backgroundColor = ThemeManager.GetBackgroundColor();
                    var textColor = ThemeManager.GetTextPrimaryColor();

                    Console.WriteLine($"   ثيم {theme}:");
                    Console.WriteLine($"     اللون الأساسي: {primaryColor}");
                    Console.WriteLine($"     لون الخلفية: {backgroundColor}");
                    Console.WriteLine($"     لون النص: {textColor}");
                }

                Console.WriteLine("   ✅ نجح اختبار الثيمات");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ فشل اختبار الثيمات: {ex.Message}");
            }

            Console.WriteLine();
        }
    }
}
