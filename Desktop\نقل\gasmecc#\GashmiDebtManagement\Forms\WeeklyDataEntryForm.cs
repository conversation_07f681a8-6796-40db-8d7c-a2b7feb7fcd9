using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Services;
using ConsoleTables;

namespace GashmiDebtManagement.Forms
{
    /// <summary>
    /// نافذة إدخال البيانات الأسبوعية
    /// </summary>
    public class WeeklyDataEntryForm
    {
        private readonly IRaayahRepository _raayahRepository;
        private readonly IWeeklyDebtsRepository _weeklyDebtsRepository;

        public WeeklyDataEntryForm(IRaayahRepository raayahRepository, IWeeklyDebtsRepository weeklyDebtsRepository)
        {
            _raayahRepository = raayahRepository;
            _weeklyDebtsRepository = weeklyDebtsRepository;
        }

        /// <summary>
        /// عرض نافذة إدخال البيانات الأسبوعية
        /// </summary>
        public async Task ShowAsync()
        {
            while (true)
            {
                Console.Clear();
                Console.WriteLine("═══════════════════════════════════════════════════════════");
                Console.WriteLine("                 إدخال البيانات الأسبوعية");
                Console.WriteLine("═══════════════════════════════════════════════════════════");
                Console.WriteLine();

                Console.WriteLine("1. إدخال بيانات أسبوع جديد");
                Console.WriteLine("2. عرض البيانات الأسبوعية");
                Console.WriteLine("3. تعديل بيانات أسبوع");
                Console.WriteLine("4. حذف بيانات أسبوع");
                Console.WriteLine("0. العودة للقائمة الرئيسية");
                Console.WriteLine();
                Console.Write("اختر العملية المطلوبة: ");

                var choice = Console.ReadLine();

                switch (choice)
                {
                    case "1":
                        await EnterWeeklyData();
                        break;
                    case "2":
                        await ViewWeeklyData();
                        break;
                    case "3":
                        await EditWeeklyData();
                        break;
                    case "4":
                        await DeleteWeeklyData();
                        break;
                    case "0":
                        return;
                    default:
                        Console.WriteLine("خيار غير صحيح، يرجى المحاولة مرة أخرى.");
                        break;
                }

                if (choice != "0")
                {
                    Console.WriteLine("\nاضغط أي مفتاح للمتابعة...");
                    Console.ReadKey();
                }
            }
        }

        /// <summary>
        /// إدخال بيانات أسبوع جديد
        /// </summary>
        private async Task EnterWeeklyData()
        {
            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                 إدخال بيانات أسبوع جديد");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                // تحديد فترة الأسبوع
                Console.Write("أدخل تاريخ بداية الأسبوع (dd/MM/yyyy): ");
                if (!DateTime.TryParseExact(Console.ReadLine(), "dd/MM/yyyy", null, System.Globalization.DateTimeStyles.None, out DateTime fromDate))
                {
                    Console.WriteLine("تاريخ غير صحيح!");
                    return;
                }

                var toDate = fromDate.AddDays(6);
                Console.WriteLine($"فترة الأسبوع: {fromDate:dd/MM/yyyy} - {toDate:dd/MM/yyyy}");

                // التحقق من وجود بيانات لهذه الفترة
                var existingData = await _weeklyDebtsRepository.GetByPeriodAsync(fromDate, toDate);
                if (existingData.Any())
                {
                    Console.WriteLine("يوجد بيانات مسجلة لهذه الفترة!");
                    Console.Write("هل تريد استبدالها؟ (y/n): ");
                    if (Console.ReadLine()?.Trim().ToLower() != "y")
                    {
                        return;
                    }
                }

                // الحصول على قائمة الرعية
                var raayahList = await _raayahRepository.GetAllAsync();
                if (!raayahList.Any())
                {
                    Console.WriteLine("لا توجد رعية مسجلة! يرجى إضافة رعية أولاً.");
                    return;
                }

                var weeklyDebtsData = new Dictionary<int, WeeklyDebts>();

                Console.WriteLine("\nإدخال البيانات لكل رعوي:");
                Console.WriteLine("(اتركه فارغاً للقيمة 0)");
                Console.WriteLine();

                foreach (var raayah in raayahList.OrderBy(r => r.FullName))
                {
                    Console.WriteLine($"الرعوي: {raayah.FullName}");

                    var weeklyDebt = new WeeklyDebts
                    {
                        RaayahId = raayah.Id,
                        DateFrom = fromDate,
                        DateTo = toDate,
                        Raayah = raayah
                    };

                    weeklyDebt.SamirAmount = GetDecimalInput("  مبلغ سمير: ");
                    weeklyDebt.MaherAmount = GetDecimalInput("  مبلغ ماهر: ");
                    weeklyDebt.RaidAmount = GetDecimalInput("  مبلغ رايد: ");
                    weeklyDebt.HaiderAmount = GetDecimalInput("  مبلغ حيدر: ");
                    weeklyDebt.LateAmount = GetDecimalInput("  المبلغ المتأخر: ");
                    weeklyDebt.ReceivedAmount = GetDecimalInput("  المبلغ الواصل: ");

                    weeklyDebtsData[raayah.Id] = weeklyDebt;
                    Console.WriteLine();
                }

                // عرض ملخص البيانات
                ShowDataSummary(weeklyDebtsData.Values);

                Console.Write("هل تريد حفظ البيانات؟ (y/n): ");
                if (Console.ReadLine()?.Trim().ToLower() == "y")
                {
                    var success = await _weeklyDebtsRepository.BulkInsertWeeklyDebtsAsync(fromDate, toDate, weeklyDebtsData);
                    if (success)
                    {
                        await _weeklyDebtsRepository.SaveChangesAsync();
                    }

                    if (success)
                    {
                        Console.WriteLine("تم حفظ البيانات بنجاح!");
                    }
                    else
                    {
                        Console.WriteLine("حدث خطأ في حفظ البيانات!");
                    }
                }
                else
                {
                    Console.WriteLine("تم إلغاء العملية.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إدخال البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// عرض البيانات الأسبوعية
        /// </summary>
        private async Task ViewWeeklyData()
        {
            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                 عرض البيانات الأسبوعية");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                Console.Write("أدخل تاريخ بداية الأسبوع (dd/MM/yyyy): ");
                if (!DateTime.TryParseExact(Console.ReadLine(), "dd/MM/yyyy", null, System.Globalization.DateTimeStyles.None, out DateTime fromDate))
                {
                    Console.WriteLine("تاريخ غير صحيح!");
                    return;
                }

                var toDate = fromDate.AddDays(6);
                var weeklyData = await _weeklyDebtsRepository.GetByPeriodWithRaayahAsync(fromDate, toDate);

                if (!weeklyData.Any())
                {
                    Console.WriteLine("لا توجد بيانات لهذه الفترة.");
                    return;
                }

                Console.WriteLine($"\nبيانات الأسبوع: {fromDate:dd/MM/yyyy} - {toDate:dd/MM/yyyy}");
                Console.WriteLine();

                var table = new ConsoleTable("الرعوي", "سمير", "ماهر", "رايد", "حيدر", "متأخر", "واصل", "إجمالي", "خصم", "صافي");

                foreach (var debt in weeklyData)
                {
                    table.AddRow(
                        debt.Raayah.FullName,
                        debt.SamirAmount.ToString("N2"),
                        debt.MaherAmount.ToString("N2"),
                        debt.RaidAmount.ToString("N2"),
                        debt.HaiderAmount.ToString("N2"),
                        debt.LateAmount.ToString("N2"),
                        debt.ReceivedAmount.ToString("N2"),
                        debt.TotalDebtsAmount.ToString("N2"),
                        debt.DiscountAmount.ToString("N2"),
                        debt.NetAmount.ToString("N2")
                    );
                }

                table.Write();

                // عرض الإجماليات
                var totals = CalculationService.CalculatePeriodTotals(weeklyData);
                Console.WriteLine();
                Console.WriteLine("الإجماليات:");
                Console.WriteLine($"إجمالي الديون: {CalculationService.FormatCurrency(totals.TotalDebts)}");
                Console.WriteLine($"إجمالي الواصل: {CalculationService.FormatCurrency(totals.TotalReceived)}");
                Console.WriteLine($"إجمالي الخصومات: {CalculationService.FormatCurrency(totals.TotalDiscount)}");
                Console.WriteLine($"الصافي: {CalculationService.FormatCurrency(totals.TotalNet)}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في عرض البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// تعديل بيانات أسبوع
        /// </summary>
        private async Task EditWeeklyData()
        {
            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                  تعديل بيانات أسبوع");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                Console.Write("أدخل تاريخ بداية الأسبوع (dd/MM/yyyy): ");
                if (!DateTime.TryParseExact(Console.ReadLine(), "dd/MM/yyyy", null, System.Globalization.DateTimeStyles.None, out DateTime fromDate))
                {
                    Console.WriteLine("تاريخ غير صحيح!");
                    return;
                }

                var toDate = fromDate.AddDays(6);
                var weeklyData = await _weeklyDebtsRepository.GetByPeriodWithRaayahAsync(fromDate, toDate);

                if (!weeklyData.Any())
                {
                    Console.WriteLine("لا توجد بيانات لهذه الفترة.");
                    return;
                }

                Console.WriteLine($"\nتعديل بيانات الأسبوع: {fromDate:dd/MM/yyyy} - {toDate:dd/MM/yyyy}");
                Console.WriteLine();

                foreach (var debt in weeklyData.OrderBy(d => d.Raayah.FullName))
                {
                    Console.WriteLine($"الرعوي: {debt.Raayah.FullName}");
                    Console.WriteLine("(اتركه فارغاً للاحتفاظ بالقيمة الحالية)");

                    debt.SamirAmount = GetDecimalInput($"  مبلغ سمير [{debt.SamirAmount}]: ", debt.SamirAmount);
                    debt.MaherAmount = GetDecimalInput($"  مبلغ ماهر [{debt.MaherAmount}]: ", debt.MaherAmount);
                    debt.RaidAmount = GetDecimalInput($"  مبلغ رايد [{debt.RaidAmount}]: ", debt.RaidAmount);
                    debt.HaiderAmount = GetDecimalInput($"  مبلغ حيدر [{debt.HaiderAmount}]: ", debt.HaiderAmount);
                    debt.LateAmount = GetDecimalInput($"  المبلغ المتأخر [{debt.LateAmount}]: ", debt.LateAmount);
                    debt.ReceivedAmount = GetDecimalInput($"  المبلغ الواصل [{debt.ReceivedAmount}]: ", debt.ReceivedAmount);

                    Console.WriteLine();
                }

                Console.Write("هل تريد حفظ التعديلات؟ (y/n): ");
                if (Console.ReadLine()?.Trim().ToLower() == "y")
                {
                    await _weeklyDebtsRepository.UpdateRangeAsync(weeklyData);
                    await _weeklyDebtsRepository.SaveChangesAsync();
                    Console.WriteLine("تم حفظ التعديلات بنجاح!");
                }
                else
                {
                    Console.WriteLine("تم إلغاء العملية.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعديل البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف بيانات أسبوع
        /// </summary>
        private async Task DeleteWeeklyData()
        {
            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                   حذف بيانات أسبوع");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                Console.Write("أدخل تاريخ بداية الأسبوع (dd/MM/yyyy): ");
                if (!DateTime.TryParseExact(Console.ReadLine(), "dd/MM/yyyy", null, System.Globalization.DateTimeStyles.None, out DateTime fromDate))
                {
                    Console.WriteLine("تاريخ غير صحيح!");
                    return;
                }

                var toDate = fromDate.AddDays(6);
                var weeklyData = await _weeklyDebtsRepository.GetByPeriodAsync(fromDate, toDate);

                if (!weeklyData.Any())
                {
                    Console.WriteLine("لا توجد بيانات لهذه الفترة.");
                    return;
                }

                Console.WriteLine($"هل أنت متأكد من حذف بيانات الأسبوع: {fromDate:dd/MM/yyyy} - {toDate:dd/MM/yyyy}؟");
                Console.WriteLine($"سيتم حذف {weeklyData.Count()} سجل.");
                Console.Write("اكتب 'نعم' للتأكيد: ");

                var confirmation = Console.ReadLine()?.Trim();
                if (confirmation != "نعم")
                {
                    Console.WriteLine("تم إلغاء العملية.");
                    return;
                }

                await _weeklyDebtsRepository.DeleteByPeriodAsync(fromDate, toDate);
                await _weeklyDebtsRepository.SaveChangesAsync();

                Console.WriteLine("تم حذف البيانات بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حذف البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على مدخل رقم عشري
        /// </summary>
        private decimal GetDecimalInput(string prompt, decimal defaultValue = 0)
        {
            Console.Write(prompt);
            var input = Console.ReadLine()?.Trim();

            if (string.IsNullOrEmpty(input))
                return defaultValue;

            if (decimal.TryParse(input, out decimal value))
                return value;

            return defaultValue;
        }

        /// <summary>
        /// عرض ملخص البيانات
        /// </summary>
        private void ShowDataSummary(IEnumerable<WeeklyDebts> debts)
        {
            Console.WriteLine("\n═══════════════════════════════════════════════════════════");
            Console.WriteLine("                      ملخص البيانات");
            Console.WriteLine("═══════════════════════════════════════════════════════════");

            var totals = CalculationService.CalculatePeriodTotals(debts);
            var branches = CalculationService.CalculateBranchesTotals(debts);

            Console.WriteLine($"إجمالي سمير: {CalculationService.FormatCurrency(branches.Samir)}");
            Console.WriteLine($"إجمالي ماهر: {CalculationService.FormatCurrency(branches.Maher)}");
            Console.WriteLine($"إجمالي رايد: {CalculationService.FormatCurrency(branches.Raid)}");
            Console.WriteLine($"إجمالي حيدر: {CalculationService.FormatCurrency(branches.Haider)}");
            Console.WriteLine($"إجمالي المتأخر: {CalculationService.FormatCurrency(branches.Late)}");
            Console.WriteLine("─────────────────────────────────────────────────────────");
            Console.WriteLine($"إجمالي الديون: {CalculationService.FormatCurrency(totals.TotalDebts)}");
            Console.WriteLine($"إجمالي الواصل: {CalculationService.FormatCurrency(totals.TotalReceived)}");
            Console.WriteLine($"إجمالي الخصومات: {CalculationService.FormatCurrency(totals.TotalDiscount)}");
            Console.WriteLine($"الصافي: {CalculationService.FormatCurrency(totals.TotalNet)}");
            Console.WriteLine();
        }
    }
}
