using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using GashmiDebtManagement.UI.DesignSystem;

namespace GashmiDebtManagement.UI.Controls
{
    /// <summary>
    /// بطاقة حديثة مع ظلال وتأثيرات بصرية متقدمة
    /// </summary>
    public class ModernCard : Panel
    {
        #region الخصائص الخاصة

        private int _elevation = 2;
        private int _borderRadius = ModernDesignSystem.Dimensions.BorderRadiusMedium;
        private Color _shadowColor = ModernDesignSystem.Shadows.Shadow2;
        private bool _isHovered = false;
        private bool _isClickable = false;
        private System.Windows.Forms.Timer _hoverTimer;
        private float _hoverProgress = 0f;
        private string _title = "";
        private string _subtitle = "";
        private string _iconText = "";

        #endregion

        #region الخصائص العامة

        /// <summary>
        /// مستوى الارتفاع (الظل)
        /// </summary>
        [Category("Appearance")]
        [Description("مستوى الارتفاع للظل (1-4)")]
        public int Elevation
        {
            get => _elevation;
            set
            {
                _elevation = Math.Max(1, Math.Min(4, value));
                UpdateShadowColor();
                Invalidate();
            }
        }

        /// <summary>
        /// نصف قطر الحواف المدورة
        /// </summary>
        [Category("Appearance")]
        [Description("نصف قطر الحواف المدورة")]
        public int BorderRadius
        {
            get => _borderRadius;
            set
            {
                _borderRadius = Math.Max(0, value);
                Invalidate();
            }
        }

        /// <summary>
        /// العنوان الرئيسي
        /// </summary>
        [Category("Appearance")]
        [Description("العنوان الرئيسي للبطاقة")]
        public string Title
        {
            get => _title;
            set
            {
                _title = value ?? "";
                Invalidate();
            }
        }

        /// <summary>
        /// العنوان الفرعي
        /// </summary>
        [Category("Appearance")]
        [Description("العنوان الفرعي للبطاقة")]
        public string Subtitle
        {
            get => _subtitle;
            set
            {
                _subtitle = value ?? "";
                Invalidate();
            }
        }

        /// <summary>
        /// نص الأيقونة
        /// </summary>
        [Category("Appearance")]
        [Description("نص الأيقونة (Emoji أو رمز)")]
        public string IconText
        {
            get => _iconText;
            set
            {
                _iconText = value ?? "";
                Invalidate();
            }
        }

        /// <summary>
        /// قابلية النقر
        /// </summary>
        [Category("Behavior")]
        [Description("جعل البطاقة قابلة للنقر")]
        public bool IsClickable
        {
            get => _isClickable;
            set
            {
                _isClickable = value;
                Cursor = value ? Cursors.Hand : Cursors.Default;
            }
        }

        #endregion

        #region الأحداث

        public event EventHandler<EventArgs>? CardClicked;

        #endregion

        #region البناء والتهيئة

        public ModernCard()
        {
            InitializeComponent();
            SetStyle(ControlStyles.AllPaintingInWmPaint | 
                     ControlStyles.UserPaint | 
                     ControlStyles.DoubleBuffer | 
                     ControlStyles.ResizeRedraw, true);

            UpdateShadowColor();

            // تهيئة مؤقت تأثير التمرير
            _hoverTimer = new System.Windows.Forms.Timer { Interval = 16 }; // 60 FPS
            _hoverTimer.Tick += HoverTimer_Tick;
        }

        private void InitializeComponent()
        {
            // إعدادات أساسية
            BackColor = ModernDesignSystem.Colors.Surface;
            Size = new Size(300, 200);
            Padding = new Padding(ModernDesignSystem.Spacing.Large);
            RightToLeft = RightToLeft.Yes;

            // إزالة الحدود الافتراضية
            BorderStyle = BorderStyle.None;
        }

        #endregion

        #region إدارة الظلال

        private void UpdateShadowColor()
        {
            _shadowColor = _elevation switch
            {
                1 => ModernDesignSystem.Shadows.Shadow1,
                2 => ModernDesignSystem.Shadows.Shadow2,
                3 => ModernDesignSystem.Shadows.Shadow3,
                _ => ModernDesignSystem.Shadows.Shadow4
            };
        }

        #endregion

        #region الرسم المخصص

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;

            var cardRect = new Rectangle(
                _elevation, 
                _elevation, 
                Width - (_elevation * 2) - 1, 
                Height - (_elevation * 2) - 1
            );

            // رسم الظل
            DrawShadow(g, cardRect);

            // رسم خلفية البطاقة
            DrawBackground(g, cardRect);

            // رسم المحتوى إذا كان هناك عنوان أو أيقونة
            if (!string.IsNullOrEmpty(_title) || !string.IsNullOrEmpty(_iconText))
            {
                DrawContent(g, cardRect);
            }

            // رسم تأثير التمرير
            if (_isClickable && _hoverProgress > 0)
            {
                DrawHoverEffect(g, cardRect);
            }
        }

        private void DrawShadow(Graphics g, Rectangle cardRect)
        {
            // رسم ظل متدرج
            var shadowRect = new Rectangle(
                cardRect.X + _elevation,
                cardRect.Y + _elevation,
                cardRect.Width,
                cardRect.Height
            );

            using var shadowBrush = new SolidBrush(_shadowColor);
            g.FillRoundedRectangle(shadowBrush, shadowRect, _borderRadius);
        }

        private void DrawBackground(Graphics g, Rectangle rect)
        {
            // رسم خلفية البطاقة
            using var backgroundBrush = new SolidBrush(BackColor);
            g.FillRoundedRectangle(backgroundBrush, rect, _borderRadius);

            // رسم حدود خفيفة
            using var borderPen = new Pen(ModernDesignSystem.Colors.Border, 1);
            g.DrawRoundedRectangle(borderPen, rect, _borderRadius);
        }

        private void DrawContent(Graphics g, Rectangle rect)
        {
            var contentRect = new Rectangle(
                rect.X + Padding.Left,
                rect.Y + Padding.Top,
                rect.Width - Padding.Horizontal,
                rect.Height - Padding.Vertical
            );

            var currentY = contentRect.Y;

            // رسم الأيقونة
            if (!string.IsNullOrEmpty(_iconText))
            {
                var iconSize = 32;
                var iconRect = new Rectangle(
                    contentRect.Right - iconSize,
                    currentY,
                    iconSize,
                    iconSize
                );

                using var iconBrush = new SolidBrush(ModernDesignSystem.Colors.Primary);
                using var iconFont = new Font(ModernDesignSystem.Typography.Body.FontFamily, iconSize * 0.6f);
                g.DrawString(_iconText, iconFont, iconBrush, iconRect,
                    new StringFormat { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center });
            }

            // رسم العنوان الرئيسي
            if (!string.IsNullOrEmpty(_title))
            {
                var titleRect = new Rectangle(
                    contentRect.X,
                    currentY,
                    contentRect.Width - (!string.IsNullOrEmpty(_iconText) ? 40 : 0),
                    (int)ModernDesignSystem.Typography.Title.Size + 8
                );

                using var titleBrush = new SolidBrush(ModernDesignSystem.Colors.TextPrimary);
                g.DrawString(_title, ModernDesignSystem.Typography.Title, titleBrush, titleRect,
                    new StringFormat { Alignment = StringAlignment.Far, LineAlignment = StringAlignment.Center });

                currentY += titleRect.Height + ModernDesignSystem.Spacing.Small;
            }

            // رسم العنوان الفرعي
            if (!string.IsNullOrEmpty(_subtitle))
            {
                var subtitleRect = new Rectangle(
                    contentRect.X,
                    currentY,
                    contentRect.Width,
                    (int)ModernDesignSystem.Typography.Body.Size + 4
                );

                using var subtitleBrush = new SolidBrush(ModernDesignSystem.Colors.TextSecondary);
                g.DrawString(_subtitle, ModernDesignSystem.Typography.Body, subtitleBrush, subtitleRect,
                    new StringFormat { Alignment = StringAlignment.Far, LineAlignment = StringAlignment.Center });
            }
        }

        private void DrawHoverEffect(Graphics g, Rectangle rect)
        {
            var alpha = (int)(50 * _hoverProgress);
            using var hoverBrush = new SolidBrush(Color.FromArgb(alpha, ModernDesignSystem.Colors.Primary));
            g.FillRoundedRectangle(hoverBrush, rect, _borderRadius);
        }

        #endregion

        #region معالجة الأحداث

        protected override void OnMouseEnter(EventArgs e)
        {
            if (_isClickable)
            {
                _isHovered = true;
                StartHoverAnimation(true);
            }
            base.OnMouseEnter(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            if (_isClickable)
            {
                _isHovered = false;
                StartHoverAnimation(false);
            }
            base.OnMouseLeave(e);
        }

        protected override void OnClick(EventArgs e)
        {
            if (_isClickable)
            {
                CardClicked?.Invoke(this, e);
            }
            base.OnClick(e);
        }

        #endregion

        #region الحركات والتأثيرات

        private void StartHoverAnimation(bool hover)
        {
            _hoverTimer.Start();
        }

        private void HoverTimer_Tick(object sender, EventArgs e)
        {
            if (_isHovered)
            {
                _hoverProgress += 0.1f;
                if (_hoverProgress >= 1f)
                {
                    _hoverProgress = 1f;
                    _hoverTimer.Stop();
                }
            }
            else
            {
                _hoverProgress -= 0.1f;
                if (_hoverProgress <= 0f)
                {
                    _hoverProgress = 0f;
                    _hoverTimer.Stop();
                }
            }

            Invalidate();
        }

        #endregion

        #region دوال مساعدة

        /// <summary>
        /// إنشاء بطاقة بسيطة
        /// </summary>
        public static ModernCard CreateSimpleCard(string title, string subtitle = "", string icon = "")
        {
            return new ModernCard
            {
                Title = title,
                Subtitle = subtitle,
                IconText = icon,
                Size = new Size(280, 120)
            };
        }

        /// <summary>
        /// إنشاء بطاقة قابلة للنقر
        /// </summary>
        public static ModernCard CreateClickableCard(string title, string subtitle = "", string icon = "", EventHandler? clickHandler = null)
        {
            var card = new ModernCard
            {
                Title = title,
                Subtitle = subtitle,
                IconText = icon,
                IsClickable = true,
                Size = new Size(280, 120)
            };

            if (clickHandler != null)
                card.CardClicked += (s, e) => clickHandler(s, e);

            return card;
        }

        /// <summary>
        /// إنشاء بطاقة إحصائية
        /// </summary>
        public static ModernCard CreateStatCard(string title, string value, string icon = "", Color? accentColor = null)
        {
            var card = new ModernCard
            {
                Title = value,
                Subtitle = title,
                IconText = icon,
                Size = new Size(200, 100),
                Elevation = 1
            };

            return card;
        }

        #endregion

        #region تنظيف الموارد

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _hoverTimer?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
