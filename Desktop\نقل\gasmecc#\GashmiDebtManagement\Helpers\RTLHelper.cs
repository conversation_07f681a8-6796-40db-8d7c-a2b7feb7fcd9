using System.Drawing;
using System.Windows.Forms;

namespace GashmiDebtManagement.Helpers
{
    /// <summary>
    /// مساعد دعم اللغة العربية والاتجاه من اليمين لليسار (RTL)
    /// </summary>
    public static class RTLHelper
    {
        #region إعدادات RTL الأساسية

        /// <summary>
        /// تطبيق إعدادات RTL على النموذج
        /// </summary>
        public static void ApplyRTL(Form form)
        {
            form.RightToLeft = RightToLeft.Yes;
            form.RightToLeftLayout = true;
            form.Font = UIHelper.ModernFonts.ArabicBody;

            // تطبيق RTL على جميع العناصر الفرعية
            ApplyRTLToControls(form);
        }

        /// <summary>
        /// تطبيق إعدادات RTL على جميع العناصر
        /// </summary>
        private static void ApplyRTLToControls(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                ApplyRTLToControl(control);

                // تطبيق على العناصر الفرعية
                if (control.HasChildren)
                {
                    ApplyRTLToControls(control);
                }
            }
        }

        /// <summary>
        /// تطبيق إعدادات RTL على عنصر واحد
        /// </summary>
        private static void ApplyRTLToControl(Control control)
        {
            control.RightToLeft = RightToLeft.Yes;

            switch (control)
            {
                case Label label:
                    ApplyRTLToLabel(label);
                    break;
                case Button button:
                    ApplyRTLToButton(button);
                    break;
                case TextBox textBox:
                    ApplyRTLToTextBox(textBox);
                    break;
                case ComboBox comboBox:
                    ApplyRTLToComboBox(comboBox);
                    break;
                case DataGridView dataGridView:
                    ApplyRTLToDataGridView(dataGridView);
                    break;
                case MenuStrip menuStrip:
                    ApplyRTLToMenuStrip(menuStrip);
                    break;
                case Panel panel:
                    ApplyRTLToPanel(panel);
                    break;
            }
        }

        #endregion

        #region تطبيق RTL على عناصر محددة

        /// <summary>
        /// تطبيق RTL على Label
        /// </summary>
        private static void ApplyRTLToLabel(Label label)
        {
            // تحديد محاذاة النص حسب المحتوى
            if (IsArabicContent(label.Text))
            {
                if (label.TextAlign == ContentAlignment.TopLeft)
                    label.TextAlign = ContentAlignment.TopRight;
                else if (label.TextAlign == ContentAlignment.MiddleLeft)
                    label.TextAlign = ContentAlignment.MiddleRight;
                else if (label.TextAlign == ContentAlignment.BottomLeft)
                    label.TextAlign = ContentAlignment.BottomRight;
            }
        }

        /// <summary>
        /// تطبيق RTL على Button
        /// </summary>
        private static void ApplyRTLToButton(Button button)
        {
            // تحديد محاذاة النص
            if (IsArabicContent(button.Text))
            {
                if (button.TextAlign == ContentAlignment.MiddleLeft)
                    button.TextAlign = ContentAlignment.MiddleRight;
                else if (button.TextAlign == ContentAlignment.TopLeft)
                    button.TextAlign = ContentAlignment.TopRight;
                else if (button.TextAlign == ContentAlignment.BottomLeft)
                    button.TextAlign = ContentAlignment.BottomRight;
            }
        }

        /// <summary>
        /// تطبيق RTL على TextBox
        /// </summary>
        private static void ApplyRTLToTextBox(TextBox textBox)
        {
            textBox.TextAlign = HorizontalAlignment.Right;
        }

        /// <summary>
        /// تطبيق RTL على ComboBox
        /// </summary>
        private static void ApplyRTLToComboBox(ComboBox comboBox)
        {
            comboBox.DropDownStyle = ComboBoxStyle.DropDownList;
        }

        /// <summary>
        /// تطبيق RTL على DataGridView
        /// </summary>
        private static void ApplyRTLToDataGridView(DataGridView dataGridView)
        {
            // تطبيق RTL على الأعمدة
            foreach (DataGridViewColumn column in dataGridView.Columns)
            {
                ApplyRTLToDataGridViewColumn(column);
            }

            // تطبيق RTL على رؤوس الأعمدة
            dataGridView.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridView.EnableHeadersVisualStyles = false;
        }

        /// <summary>
        /// تطبيق RTL على عمود DataGridView
        /// </summary>
        private static void ApplyRTLToDataGridViewColumn(DataGridViewColumn column)
        {
            // تحديد المحاذاة حسب نوع البيانات
            if (column.Name.Contains("Name") || column.Name.Contains("اسم"))
            {
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            }
            else if (column.Name.Contains("Amount") || column.Name.Contains("مبلغ") || 
                     column.Name.Contains("Total") || column.Name.Contains("إجمالي"))
            {
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                column.DefaultCellStyle.Format = "N2";
            }
            else if (column.Name.Contains("Date") || column.Name.Contains("تاريخ"))
            {
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                column.DefaultCellStyle.Format = "dd/MM/yyyy";
            }
            else if (column.Name.Contains("Id") || column.Name.Contains("رقم"))
            {
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            }
            else
            {
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            }
        }

        /// <summary>
        /// تطبيق RTL على MenuStrip
        /// </summary>
        private static void ApplyRTLToMenuStrip(MenuStrip menuStrip)
        {
            menuStrip.LayoutStyle = ToolStripLayoutStyle.HorizontalStackWithOverflow;
            
            // تطبيق على عناصر القائمة
            foreach (ToolStripMenuItem item in menuStrip.Items)
            {
                ApplyRTLToMenuItem(item);
            }
        }

        /// <summary>
        /// تطبيق RTL على عنصر القائمة
        /// </summary>
        private static void ApplyRTLToMenuItem(ToolStripMenuItem item)
        {
            item.RightToLeft = RightToLeft.Yes;
            
            // تطبيق على العناصر الفرعية
            foreach (ToolStripItem subItem in item.DropDownItems)
            {
                if (subItem is ToolStripMenuItem menuItem)
                {
                    ApplyRTLToMenuItem(menuItem);
                }
            }
        }

        /// <summary>
        /// تطبيق RTL على Panel
        /// </summary>
        private static void ApplyRTLToPanel(Panel panel)
        {
            // تحديد اتجاه التدفق للوحات
            if (panel is FlowLayoutPanel flowPanel)
            {
                flowPanel.FlowDirection = FlowDirection.RightToLeft;
            }
            else if (panel is TableLayoutPanel tablePanel)
            {
                // تطبيق RTL على TableLayoutPanel
                ApplyRTLToTableLayoutPanel(tablePanel);
            }
        }

        /// <summary>
        /// تطبيق RTL على TableLayoutPanel
        /// </summary>
        private static void ApplyRTLToTableLayoutPanel(TableLayoutPanel tablePanel)
        {
            // عكس ترتيب الأعمدة للـ RTL
            var columnCount = tablePanel.ColumnCount;
            for (int i = 0; i < columnCount / 2; i++)
            {
                var leftStyle = tablePanel.ColumnStyles[i];
                var rightStyle = tablePanel.ColumnStyles[columnCount - 1 - i];
                
                tablePanel.ColumnStyles[i] = rightStyle;
                tablePanel.ColumnStyles[columnCount - 1 - i] = leftStyle;
            }
        }

        #endregion

        #region وظائف مساعدة

        /// <summary>
        /// التحقق من وجود محتوى عربي
        /// </summary>
        private static bool IsArabicContent(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            return text.Any(c => c >= 0x0600 && c <= 0x06FF);
        }

        /// <summary>
        /// تحديد المحاذاة المناسبة للنص العربي
        /// </summary>
        public static ContentAlignment GetArabicAlignment(ContentAlignment originalAlignment)
        {
            return originalAlignment switch
            {
                ContentAlignment.TopLeft => ContentAlignment.TopRight,
                ContentAlignment.MiddleLeft => ContentAlignment.MiddleRight,
                ContentAlignment.BottomLeft => ContentAlignment.BottomRight,
                ContentAlignment.TopRight => ContentAlignment.TopLeft,
                ContentAlignment.MiddleRight => ContentAlignment.MiddleLeft,
                ContentAlignment.BottomRight => ContentAlignment.BottomLeft,
                _ => originalAlignment
            };
        }

        /// <summary>
        /// تحديد المحاذاة الأفقية للنص العربي
        /// </summary>
        public static HorizontalAlignment GetArabicHorizontalAlignment(HorizontalAlignment originalAlignment)
        {
            return originalAlignment switch
            {
                HorizontalAlignment.Left => HorizontalAlignment.Right,
                HorizontalAlignment.Right => HorizontalAlignment.Left,
                _ => originalAlignment
            };
        }

        /// <summary>
        /// تحديد نقطة الإرساء للعناصر العربية
        /// </summary>
        public static AnchorStyles GetArabicAnchor(AnchorStyles originalAnchor)
        {
            var newAnchor = originalAnchor;

            if (originalAnchor.HasFlag(AnchorStyles.Left) && !originalAnchor.HasFlag(AnchorStyles.Right))
            {
                newAnchor = (originalAnchor & ~AnchorStyles.Left) | AnchorStyles.Right;
            }
            else if (originalAnchor.HasFlag(AnchorStyles.Right) && !originalAnchor.HasFlag(AnchorStyles.Left))
            {
                newAnchor = (originalAnchor & ~AnchorStyles.Right) | AnchorStyles.Left;
            }

            return newAnchor;
        }

        /// <summary>
        /// تحديد موقع العنصر للـ RTL
        /// </summary>
        public static Point GetRTLLocation(Point originalLocation, int containerWidth, int elementWidth)
        {
            var newX = containerWidth - originalLocation.X - elementWidth;
            return new Point(newX, originalLocation.Y);
        }

        #endregion
    }
}
