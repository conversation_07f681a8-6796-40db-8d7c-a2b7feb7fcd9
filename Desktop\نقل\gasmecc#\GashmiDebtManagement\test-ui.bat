@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo.
echo ========================================
echo    🧪 اختبارات واجهة المستخدم الشاملة
echo ========================================
echo.

REM التحقق من وجود المشروع
if not exist "GashmiDebtManagement.csproj" (
    echo ❌ لم يتم العثور على ملف المشروع
    echo تأكد من تشغيل هذا الملف من مجلد المشروع
    pause
    exit /b 1
)

echo 🔧 جاري بناء المشروع...
dotnet build --configuration Release --verbosity quiet
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    echo جاري المحاولة مع تفاصيل أكثر...
    dotnet build --configuration Release
    pause
    exit /b 1
)
echo ✅ تم بناء المشروع بنجاح

echo.
echo 🗃️ جاري التحقق من قاعدة البيانات...
if not exist "Data" mkdir Data
if not exist "Data\GashmiDebtManagement.db" (
    echo ℹ️ إنشاء قاعدة البيانات...
    dotnet run --configuration Release --no-build -- --setup-db-only
)
echo ✅ قاعدة البيانات جاهزة

echo.
echo 🧪 بدء الاختبارات الشاملة...
echo.

REM تشغيل الاختبارات
dotnet run --configuration Release --no-build -- --run-ui-tests

echo.
echo ========================================
echo    انتهت اختبارات واجهة المستخدم
echo ========================================
echo.

REM عرض ملخص النتائج
if exist "Logs\ui-test-results.log" (
    echo 📋 ملخص النتائج:
    type "Logs\ui-test-results.log"
    echo.
)

echo 💡 نصائح:
echo ├─ إذا وجدت مشاكل، راجع ملفات السجل في مجلد Logs
echo ├─ يمكنك تشغيل التطبيق يدوياً باستخدام run.bat
echo └─ للمساعدة، راجع ملف README.md

echo.
pause
