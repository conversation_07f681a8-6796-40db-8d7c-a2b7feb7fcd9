using GashmiDebtManagement.Models;

namespace GashmiDebtManagement.Repositories
{
    /// <summary>
    /// واجهة Repository خاصة بالرعية
    /// </summary>
    public interface IRaayahRepository : IRepository<Raayah>
    {
        /// <summary>
        /// البحث عن رعوي بالاسم
        /// </summary>
        Task<Raayah?> GetByNameAsync(string fullName);

        /// <summary>
        /// الحصول على الرعية مع ديونهم الأسبوعية
        /// </summary>
        Task<IEnumerable<Raayah>> GetWithWeeklyDebtsAsync();

        /// <summary>
        /// الحصول على رعوي مع ديونه الأسبوعية
        /// </summary>
        Task<Raayah?> GetWithWeeklyDebtsAsync(int id);

        /// <summary>
        /// الحصول على الرعية المفعلين لخصم الحوالة
        /// </summary>
        Task<IEnumerable<Raayah>> GetWithDiscountEnabledAsync();

        /// <summary>
        /// الحصول على الرعية في كشف الأوزري
        /// </summary>
        Task<IEnumerable<Raayah>> GetInKashfOzriAsync();

        /// <summary>
        /// الحصول على الرعية في خارج الكشف
        /// </summary>
        Task<IEnumerable<Raayah>> GetInKharijKashfAsync();

        /// <summary>
        /// الحصول على الرعية مع ديونهم لفترة معينة
        /// </summary>
        Task<IEnumerable<Raayah>> GetWithDebtsForPeriodAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// التحقق من وجود رعوي بنفس الاسم
        /// </summary>
        Task<bool> IsNameExistsAsync(string fullName, int? excludeId = null);

        /// <summary>
        /// الحصول على إحصائيات الرعية
        /// </summary>
        Task<(int Total, int WithDiscount, int InOzri, int InKharij)> GetStatisticsAsync();
    }
}
