using GashmiDebtManagement.Models;
using GashmiDebtManagement.Services;

namespace GashmiDebtManagement.Tests
{
    /// <summary>
    /// اختبارات العمليات الحسابية
    /// </summary>
    public static class CalculationTests
    {
        /// <summary>
        /// تشغيل جميع الاختبارات
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                    اختبارات العمليات الحسابية");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            var testResults = new List<(string TestName, bool Passed, string Details)>();

            // اختبار حساب إجمالي ديون الفروع
            testResults.Add(TestTotalBranchesAmount());

            // اختبار حساب إجمالي الديون
            testResults.Add(TestTotalDebtsAmount());

            // اختبار حساب خصم الحوالة
            testResults.Add(TestDiscountAmount());

            // اختبار حساب الصافي
            testResults.Add(TestNetAmount());

            // اختبار التحقق من فترة الأسبوع
            testResults.Add(TestWeekPeriodValidation());

            // اختبار تنسيق العملة
            testResults.Add(TestCurrencyFormatting());

            // اختبار حساب النسب المئوية
            testResults.Add(TestPercentageCalculation());

            // عرض النتائج
            DisplayTestResults(testResults);
        }

        /// <summary>
        /// اختبار حساب إجمالي ديون الفروع
        /// </summary>
        private static (string, bool, string) TestTotalBranchesAmount()
        {
            var debt = new WeeklyDebts
            {
                SamirAmount = 1000,
                MaherAmount = 2000,
                RaidAmount = 1500,
                HaiderAmount = 2500,
                LateAmount = 500
            };

            var expected = 7000m; // 1000 + 2000 + 1500 + 2500
            var actual = CalculationService.CalculateTotalBranchesAmount(debt);

            var passed = actual == expected;
            var details = $"المتوقع: {expected}, الفعلي: {actual}";

            return ("حساب إجمالي ديون الفروع", passed, details);
        }

        /// <summary>
        /// اختبار حساب إجمالي الديون
        /// </summary>
        private static (string, bool, string) TestTotalDebtsAmount()
        {
            var debt = new WeeklyDebts
            {
                SamirAmount = 1000,
                MaherAmount = 2000,
                RaidAmount = 1500,
                HaiderAmount = 2500,
                LateAmount = 500
            };

            var expected = 7500m; // 7000 + 500
            var actual = CalculationService.CalculateTotalDebtsAmount(debt);

            var passed = actual == expected;
            var details = $"المتوقع: {expected}, الفعلي: {actual}";

            return ("حساب إجمالي الديون", passed, details);
        }

        /// <summary>
        /// اختبار حساب خصم الحوالة
        /// </summary>
        private static (string, bool, string) TestDiscountAmount()
        {
            var debt = new WeeklyDebts
            {
                SamirAmount = 1000,
                MaherAmount = 2000,
                RaidAmount = 1500,
                HaiderAmount = 2500,
                LateAmount = 500,
                ReceivedAmount = 1000
            };

            // إجمالي الديون = 7500
            // الواصل = 1000
            // خصم الحوالة = (7500 - 1000) * 0.03 = 6500 * 0.03 = 195
            var expected = 195m;
            var actual = CalculationService.CalculateDiscountAmount(debt, true);

            var passed = actual == expected;
            var details = $"المتوقع: {expected}, الفعلي: {actual}";

            return ("حساب خصم الحوالة", passed, details);
        }

        /// <summary>
        /// اختبار حساب الصافي
        /// </summary>
        private static (string, bool, string) TestNetAmount()
        {
            var debt = new WeeklyDebts
            {
                SamirAmount = 1000,
                MaherAmount = 2000,
                RaidAmount = 1500,
                HaiderAmount = 2500,
                LateAmount = 500,
                ReceivedAmount = 1000
            };

            // إجمالي الديون = 7500
            // الواصل = 1000
            // خصم الحوالة = 195
            // الصافي = 7500 - 1000 - 195 = 6305
            var expected = 6305m;
            var actual = CalculationService.CalculateNetAmount(debt, true);

            var passed = actual == expected;
            var details = $"المتوقع: {expected}, الفعلي: {actual}";

            return ("حساب الصافي", passed, details);
        }

        /// <summary>
        /// اختبار التحقق من فترة الأسبوع
        /// </summary>
        private static (string, bool, string) TestWeekPeriodValidation()
        {
            var fromDate = new DateTime(2024, 1, 1);
            var toDate = new DateTime(2024, 1, 7);

            var isValid = CalculationService.IsValidWeekPeriod(fromDate, toDate);
            var passed = isValid;
            var details = $"من {fromDate:dd/MM/yyyy} إلى {toDate:dd/MM/yyyy} - صحيح: {isValid}";

            return ("التحقق من فترة الأسبوع", passed, details);
        }

        /// <summary>
        /// اختبار تنسيق العملة
        /// </summary>
        private static (string, bool, string) TestCurrencyFormatting()
        {
            var amount = 1234.56m;
            var formatted = CalculationService.FormatCurrency(amount);
            var expected = "1,234.56 ريال";

            var passed = formatted == expected;
            var details = $"المتوقع: '{expected}', الفعلي: '{formatted}'";

            return ("تنسيق العملة", passed, details);
        }

        /// <summary>
        /// اختبار حساب النسب المئوية
        /// </summary>
        private static (string, bool, string) TestPercentageCalculation()
        {
            var amount = 250m;
            var total = 1000m;
            var percentage = CalculationService.CalculatePercentage(amount, total);
            var expected = 25m;

            var passed = percentage == expected;
            var details = $"المتوقع: {expected}%, الفعلي: {percentage}%";

            return ("حساب النسب المئوية", passed, details);
        }

        /// <summary>
        /// عرض نتائج الاختبارات
        /// </summary>
        private static void DisplayTestResults(List<(string TestName, bool Passed, string Details)> results)
        {
            var passedCount = results.Count(r => r.Passed);
            var totalCount = results.Count;

            Console.WriteLine("نتائج الاختبارات:");
            Console.WriteLine("─────────────────────────────────────────────────────────");

            foreach (var result in results)
            {
                var status = result.Passed ? "✓ نجح" : "✗ فشل";
                var color = result.Passed ? ConsoleColor.Green : ConsoleColor.Red;
                
                Console.ForegroundColor = color;
                Console.WriteLine($"{status} - {result.TestName}");
                Console.ResetColor();
                Console.WriteLine($"   التفاصيل: {result.Details}");
                Console.WriteLine();
            }

            Console.WriteLine("─────────────────────────────────────────────────────────");
            Console.WriteLine($"الإجمالي: {passedCount}/{totalCount} اختبار نجح");

            if (passedCount == totalCount)
            {
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine("🎉 جميع الاختبارات نجحت!");
            }
            else
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"⚠️  {totalCount - passedCount} اختبار فشل!");
            }
            Console.ResetColor();
        }

        /// <summary>
        /// اختبار شامل للسيناريوهات المختلفة
        /// </summary>
        public static void RunIntegrationTest()
        {
            Console.WriteLine("\n═══════════════════════════════════════════════════════════");
            Console.WriteLine("                    اختبار التكامل الشامل");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            // إنشاء بيانات تجريبية
            var raayah1 = new Raayah
            {
                Id = 1,
                FullName = "أحمد محمد",
                EnableDiscount = true,
                InKashfOzri = true,
                InKharijKashf = false
            };

            var raayah2 = new Raayah
            {
                Id = 2,
                FullName = "محمد علي",
                EnableDiscount = false,
                InKashfOzri = false,
                InKharijKashf = true
            };

            var debts = new List<WeeklyDebts>
            {
                new WeeklyDebts
                {
                    RaayahId = 1,
                    Raayah = raayah1,
                    SamirAmount = 1000,
                    MaherAmount = 1500,
                    RaidAmount = 2000,
                    HaiderAmount = 2500,
                    LateAmount = 500,
                    ReceivedAmount = 1000,
                    DateFrom = new DateTime(2024, 1, 1),
                    DateTo = new DateTime(2024, 1, 7)
                },
                new WeeklyDebts
                {
                    RaayahId = 2,
                    Raayah = raayah2,
                    SamirAmount = 800,
                    MaherAmount = 1200,
                    RaidAmount = 1000,
                    HaiderAmount = 1500,
                    LateAmount = 300,
                    ReceivedAmount = 500,
                    DateFrom = new DateTime(2024, 1, 1),
                    DateTo = new DateTime(2024, 1, 7)
                }
            };

            // حساب الإجماليات
            var totals = CalculationService.CalculatePeriodTotals(debts);
            var branches = CalculationService.CalculateBranchesTotals(debts);

            Console.WriteLine("البيانات التجريبية:");
            Console.WriteLine($"الرعوي الأول: {raayah1.FullName} (خصم مفعل)");
            Console.WriteLine($"الرعوي الثاني: {raayah2.FullName} (خصم معطل)");
            Console.WriteLine();

            Console.WriteLine("النتائج المحسوبة:");
            Console.WriteLine($"إجمالي سمير: {CalculationService.FormatCurrency(branches.Samir)}");
            Console.WriteLine($"إجمالي ماهر: {CalculationService.FormatCurrency(branches.Maher)}");
            Console.WriteLine($"إجمالي رايد: {CalculationService.FormatCurrency(branches.Raid)}");
            Console.WriteLine($"إجمالي حيدر: {CalculationService.FormatCurrency(branches.Haider)}");
            Console.WriteLine($"إجمالي المتأخر: {CalculationService.FormatCurrency(branches.Late)}");
            Console.WriteLine("─────────────────────────────────────────────────────────");
            Console.WriteLine($"إجمالي الديون: {CalculationService.FormatCurrency(totals.TotalDebts)}");
            Console.WriteLine($"إجمالي الواصل: {CalculationService.FormatCurrency(totals.TotalReceived)}");
            Console.WriteLine($"إجمالي الخصومات: {CalculationService.FormatCurrency(totals.TotalDiscount)}");
            Console.WriteLine($"الصافي النهائي: {CalculationService.FormatCurrency(totals.TotalNet)}");

            Console.WriteLine("\n✅ اختبار التكامل مكتمل بنجاح!");
        }
    }
}
