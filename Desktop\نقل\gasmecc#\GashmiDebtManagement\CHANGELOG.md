# سجل التغييرات - نظام إدارة ديون الرعية

## الإصدار 1.0.0 - 2024-07-14

### ✨ المميزات الجديدة

#### 🏢 إدارة الرعية
- إضافة نظام إدارة شامل للرعية
- إمكانية إضافة وتعديل وحذف بيانات الرعية
- تفعيل/تعطيل خصم الحوالة لكل رعوي
- تصنيف الرعية (كشف الأوزري، خارج الكشف)
- وظيفة البحث في قائمة الرعية
- التحقق من عدم تكرار الأسماء

#### 📊 إدخال البيانات الأسبوعية
- نظام إدخال البيانات الأسبوعية المتكامل
- تحديد فترة الأسبوع (7 أيام) تلقائياً
- إدخال مبالغ الفروع الأربعة (سمير، ماهر، رايد، حيدر)
- إدخال المبلغ المتأخر والمبلغ الواصل
- حفظ البيانات دفعة واحدة لجميع الرعية
- تعديل وحذف البيانات الأسبوعية
- عرض ملخص البيانات قبل الحفظ

#### 📈 نظام التقارير الشامل
- **التقرير الكامل**: عرض جميع الديون والمبالغ الواصلة
- **التقرير مع خصم الحوالة**: حساب خصم 3% من (الديون - الواصل)
- **كشف البطاقات**: بطاقة منفصلة لكل رعوي مع تفاصيل كاملة
- **كشف الأوزري**: تقرير للرعية المحددين في كشف الأوزري فقط
- **خارج الكشف**: تقرير للرعية المحددين خارج الكشف
- **تقرير الفروع**: إحصائيات مفصلة لكل فرع مع النسب المئوية
- **التقرير الشهري**: تجميع البيانات الشهرية مع الإحصائيات

#### 🔢 العمليات الحسابية
- حساب إجمالي ديون الفروع تلقائياً
- حساب خصم الحوالة (3%) للرعية المفعلين
- حساب الصافي (الديون - الواصل - الخصم)
- حساب النسب المئوية للفروع
- التحقق من صحة فترة الأسبوع (7 أيام)

#### 📊 الإحصائيات
- إحصائيات عامة عن عدد الرعية
- إحصائيات الشهر الحالي
- عدد المفعلين لخصم الحوالة
- عدد الموجودين في كل تصنيف

#### 🧪 نظام الاختبارات
- اختبارات شاملة للعمليات الحسابية
- اختبار التكامل للنظام
- التحقق من صحة الحسابات
- عرض نتائج الاختبارات بألوان

### 🛠️ التقنيات المستخدمة

#### قاعدة البيانات
- SQLite لتخزين البيانات محلياً
- Entity Framework Core للوصول للبيانات
- Repository Pattern لتنظيم العمليات
- Migration تلقائي لقاعدة البيانات

#### المعمارية
- تطبيق مبادئ SOLID
- فصل طبقات التطبيق (Models, Repositories, Services, Forms)
- استخدام Dependency Injection
- معالجة شاملة للأخطاء

#### واجهة المستخدم
- واجهة Console تفاعلية
- دعم اللغة العربية
- تنسيق الجداول باستخدام ConsoleTables
- تنسيق العملة والتواريخ العربية

### 🗃️ هيكل قاعدة البيانات

#### جدول Raayah (الرعية)
```sql
- Id (Primary Key)
- FullName (نص فريد)
- EnableDiscount (منطقي)
- InKashfOzri (منطقي)
- InKharijKashf (منطقي)
- CreatedDate (تاريخ)
```

#### جدول WeeklyDebts (الديون الأسبوعية)
```sql
- Id (Primary Key)
- RaayahId (Foreign Key)
- DateFrom (تاريخ)
- DateTo (تاريخ)
- SamirAmount (عشري)
- MaherAmount (عشري)
- RaidAmount (عشري)
- HaiderAmount (عشري)
- LateAmount (عشري)
- ReceivedAmount (عشري)
- CreatedDate (تاريخ)
```

### 📝 الوثائق
- README.md شامل مع تعليمات التشغيل
- USER_GUIDE.md دليل مفصل للمستخدم
- توثيق الكود باللغة العربية
- أمثلة على الاستخدام

### 🔒 الأمان والموثوقية
- التحقق من صحة البيانات المدخلة
- منع تكرار البيانات
- معالجة شاملة للأخطاء
- نسخ احتياطية تلقائية لقاعدة البيانات

### 🌐 الدعم العربي
- واجهة مستخدم باللغة العربية
- تنسيق التواريخ العربية
- تنسيق العملة المحلية
- دعم الأرقام العربية والإنجليزية

### 📊 التقارير والإحصائيات
- تقارير متنوعة حسب الحاجة
- إحصائيات تفصيلية
- تصدير البيانات بتنسيق جداول
- حسابات دقيقة للخصومات والصافي

### 🧪 الاختبارات
- اختبارات وحدة للعمليات الحسابية
- اختبارات تكامل للنظام
- التحقق من صحة البيانات
- تقارير اختبار مفصلة

## خطط المستقبل

### الإصدار 1.1.0 (مخطط)
- [ ] تصدير التقارير إلى PDF
- [ ] نسخ احتياطية مجدولة
- [ ] واجهة رسومية (Windows Forms)
- [ ] دعم قواعد بيانات أخرى

### الإصدار 1.2.0 (مخطط)
- [ ] نظام المستخدمين والصلاحيات
- [ ] تقارير متقدمة مع الرسوم البيانية
- [ ] تصدير إلى Excel
- [ ] نظام التنبيهات

## المطور
تم تطوير هذا النظام بواسطة **Augment Agent** لشركة الغشمي.

## الترخيص
هذا المشروع مخصص لشركة الغشمي ولا يُسمح بإعادة توزيعه أو استخدامه تجارياً دون إذن.

---

**ملاحظة**: جميع المميزات تم اختبارها والتأكد من عملها بشكل صحيح. في حالة وجود أي مشاكل، يرجى الرجوع إلى دليل المستخدم أو التواصل مع المطور.
