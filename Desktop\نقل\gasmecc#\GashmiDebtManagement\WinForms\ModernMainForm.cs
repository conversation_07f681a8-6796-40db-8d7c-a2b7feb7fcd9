using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Services;
using GashmiDebtManagement.Helpers;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// النافذة الرئيسية المحسنة للتطبيق
    /// </summary>
    public partial class ModernMainForm : Form
    {
        private readonly ApplicationDbContext _context;
        private readonly IRaayahRepository _raayahRepository;
        private readonly IWeeklyDebtsRepository _weeklyDebtsRepository;

        // عناصر التحكم المحسنة
        private Panel? sidebarPanel;
        private Panel? mainContentPanel;
        private Panel? headerPanel;
        private Panel? statusPanel;
        private Label? titleLabel;
        private Label? statusLabel;
        private Button? dashboardButton;
        private Button? raayahButton;
        private Button? weeklyDataButton;
        private Button? reportsButton;
        private Button? toolsButton;
        private Button? settingsButton;

        public ModernMainForm()
        {
            InitializeComponent();
            InitializeModernDesign();

            _context = new ApplicationDbContext();
            _raayahRepository = new RaayahRepository(_context);
            _weeklyDebtsRepository = new WeeklyDebtsRepository(_context);

            // التأكد من إنشاء قاعدة البيانات
            _context.EnsureCreated();

            // نسخة احتياطية تلقائية
            BackupService.AutoBackupOnStartup();

            // إضافة اختصارات لوحة المفاتيح
            UIHelper.AddKeyboardShortcuts(this);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النافذة الرئيسية
            this.Text = "نظام إدارة ديون الرعية - شركة الغشمي";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.MinimumSize = new Size(1200, 700);
            this.Icon = SystemIcons.Application;
            this.BackColor = UIHelper.ModernColors.Background;

            // إنشاء التخطيط الحديث
            CreateModernLayout();

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void InitializeModernDesign()
        {
            // دعم اللغة العربية
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = UIHelper.ModernFonts.ArabicBody;
        }

        private void CreateModernLayout()
        {
            // إنشاء الرأس
            CreateHeaderPanel();

            // إنشاء الشريط الجانبي
            CreateSidebarPanel();

            // إنشاء المحتوى الرئيسي
            CreateMainContentPanel();

            // إنشاء شريط الحالة
            CreateStatusPanel();
        }

        private void CreateHeaderPanel()
        {
            headerPanel = new Panel
            {
                Height = 70,
                Dock = DockStyle.Top,
                BackColor = UIHelper.ModernColors.Primary,
                Padding = new Padding(UIHelper.Spacing.Large)
            };

            titleLabel = new Label
            {
                Text = "نظام إدارة ديون الرعية",
                Font = UIHelper.ModernFonts.ArabicHeaderLarge,
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(UIHelper.Spacing.Large, 20)
            };

            var companyLabel = new Label
            {
                Text = "شركة الغشمي",
                Font = UIHelper.ModernFonts.ArabicBody,
                ForeColor = Color.FromArgb(200, 255, 255, 255),
                AutoSize = true,
                Location = new Point(UIHelper.Spacing.Large, 45)
            };

            headerPanel.Controls.AddRange(new Control[] { titleLabel, companyLabel });
            this.Controls.Add(headerPanel);
        }

        private void CreateSidebarPanel()
        {
            sidebarPanel = new Panel
            {
                Width = 250,
                Dock = DockStyle.Right,
                BackColor = UIHelper.ModernColors.Surface,
                Padding = new Padding(UIHelper.Spacing.Medium)
            };

            // إضافة حدود
            sidebarPanel.Paint += (s, e) => {
                using var pen = new Pen(UIHelper.ModernColors.Border);
                e.Graphics.DrawLine(pen, 0, 0, 0, sidebarPanel.Height);
            };

            CreateSidebarButtons();
            this.Controls.Add(sidebarPanel);
        }

        private void CreateSidebarButtons()
        {
            var buttonHeight = UIHelper.Sizes.ButtonHeight + UIHelper.Spacing.Small;
            var currentY = UIHelper.Spacing.Large;

            // زر لوحة المعلومات
            dashboardButton = CreateSidebarButton("📊 لوحة المعلومات", currentY, ShowDashboard);
            currentY += buttonHeight;

            // زر إدارة الرعية
            raayahButton = CreateSidebarButton("👥 إدارة الرعية", currentY, ShowRaayahManagement);
            currentY += buttonHeight;

            // زر البيانات الأسبوعية
            weeklyDataButton = CreateSidebarButton("📅 البيانات الأسبوعية", currentY, ShowWeeklyData);
            currentY += buttonHeight;

            // زر التقارير
            reportsButton = CreateSidebarButton("📈 التقارير", currentY, ShowReports);
            currentY += buttonHeight;

            // زر الأدوات
            toolsButton = CreateSidebarButton("🔧 الأدوات", currentY, ShowTools);
            currentY += buttonHeight;

            // زر الإعدادات
            settingsButton = CreateSidebarButton("⚙️ الإعدادات", currentY, ShowSettings);

            sidebarPanel!.Controls.AddRange(new Control[] {
                dashboardButton, raayahButton, weeklyDataButton,
                reportsButton, toolsButton, settingsButton
            });
        }

        private Button CreateSidebarButton(string text, int y, EventHandler clickHandler)
        {
            var button = new Button
            {
                Text = text,
                Width = sidebarPanel!.Width - (UIHelper.Spacing.Medium * 2),
                Height = UIHelper.Sizes.ButtonHeight,
                Location = new Point(UIHelper.Spacing.Medium, y),
                BackColor = UIHelper.ModernColors.Surface,
                ForeColor = UIHelper.ModernColors.TextPrimary,
                Font = UIHelper.ModernFonts.ArabicBody,
                FlatStyle = FlatStyle.Flat,
                TextAlign = ContentAlignment.MiddleRight,
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.BorderColor = UIHelper.ModernColors.Border;

            // تأثيرات التفاعل
            button.MouseEnter += (s, e) => {
                button.BackColor = UIHelper.ModernColors.Hover;
                button.FlatAppearance.BorderColor = UIHelper.ModernColors.Primary;
            };

            button.MouseLeave += (s, e) => {
                button.BackColor = UIHelper.ModernColors.Surface;
                button.FlatAppearance.BorderColor = UIHelper.ModernColors.Border;
            };

            button.Click += clickHandler;
            return button;
        }

        private void CreateMainContentPanel()
        {
            mainContentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = UIHelper.ModernColors.Background,
                Padding = new Padding(UIHelper.Spacing.Large)
            };

            // إضافة رسالة ترحيب افتراضية
            ShowWelcomeMessage();

            this.Controls.Add(mainContentPanel);
        }

        private void CreateStatusPanel()
        {
            statusPanel = new Panel
            {
                Height = UIHelper.Sizes.StatusBarHeight + UIHelper.Spacing.Small,
                Dock = DockStyle.Bottom,
                BackColor = UIHelper.ModernColors.Surface,
                Padding = new Padding(UIHelper.Spacing.Medium, UIHelper.Spacing.Small)
            };

            statusLabel = new Label
            {
                Text = "جاهز",
                Font = UIHelper.ModernFonts.Caption,
                ForeColor = UIHelper.ModernColors.TextSecondary,
                AutoSize = true,
                Location = new Point(UIHelper.Spacing.Medium, UIHelper.Spacing.Small)
            };

            var versionLabel = new Label
            {
                Text = "الإصدار 1.0.0",
                Font = UIHelper.ModernFonts.Caption,
                ForeColor = UIHelper.ModernColors.TextMuted,
                AutoSize = true,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left
            };

            versionLabel.Location = new Point(
                statusPanel.Width - versionLabel.Width - UIHelper.Spacing.Medium,
                UIHelper.Spacing.Small
            );

            statusPanel.Controls.AddRange(new Control[] { statusLabel, versionLabel });
            this.Controls.Add(statusPanel);
        }

        private void ShowWelcomeMessage()
        {
            mainContentPanel!.Controls.Clear();

            var welcomeCard = UIHelper.CreateCardPanel();
            welcomeCard.Size = new Size(600, 400);
            welcomeCard.Location = new Point(
                (mainContentPanel.Width - welcomeCard.Width) / 2,
                (mainContentPanel.Height - welcomeCard.Height) / 2
            );

            var welcomeLabel = new Label
            {
                Text = "مرحباً بك في نظام إدارة ديون الرعية\n\nشركة الغشمي\n\nاختر من القائمة الجانبية للبدء",
                Font = UIHelper.ModernFonts.ArabicHeaderMedium,
                ForeColor = UIHelper.ModernColors.TextPrimary,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            welcomeCard.Controls.Add(welcomeLabel);
            mainContentPanel.Controls.Add(welcomeCard);
        }

        #region معالجات الأحداث

        private void ShowDashboard(object? sender, EventArgs e)
        {
            UpdateStatus("عرض لوحة المعلومات...");

            mainContentPanel!.Controls.Clear();

            // إنشاء لوحة معلومات بسيطة
            var dashboardPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = UIHelper.ModernColors.Background
            };

            // بطاقات الإحصائيات
            CreateStatisticsCards(dashboardPanel);

            mainContentPanel.Controls.Add(dashboardPanel);
            UpdateStatus("تم عرض لوحة المعلومات");
        }

        private void CreateStatisticsCards(Panel parent)
        {
            var cardWidth = 250;
            var cardHeight = 120;
            var spacing = UIHelper.Spacing.Large;
            var currentX = spacing;
            var currentY = spacing;

            // بطاقة عدد الرعية
            var raayahCard = CreateStatCard("عدد الرعية", "0", UIHelper.ModernColors.Primary, currentX, currentY);
            currentX += cardWidth + spacing;

            // بطاقة البيانات الأسبوعية
            var weeklyCard = CreateStatCard("البيانات الأسبوعية", "0", UIHelper.ModernColors.Success, currentX, currentY);
            currentX += cardWidth + spacing;

            // بطاقة إجمالي الديون
            var debtsCard = CreateStatCard("إجمالي الديون", "0 ريال", UIHelper.ModernColors.Warning, currentX, currentY);

            parent.Controls.AddRange(new Control[] { raayahCard, weeklyCard, debtsCard });

            // تحديث البيانات
            UpdateStatisticsAsync();
        }

        private Panel CreateStatCard(string title, string value, Color color, int x, int y)
        {
            var card = UIHelper.CreateCardPanel();
            card.Size = new Size(250, 120);
            card.Location = new Point(x, y);

            var titleLabel = new Label
            {
                Text = title,
                Font = UIHelper.ModernFonts.ArabicBody,
                ForeColor = UIHelper.ModernColors.TextSecondary,
                Location = new Point(UIHelper.Spacing.Medium, UIHelper.Spacing.Medium),
                AutoSize = true
            };

            var valueLabel = new Label
            {
                Text = value,
                Font = UIHelper.ModernFonts.ArabicHeaderMedium,
                ForeColor = color,
                Location = new Point(UIHelper.Spacing.Medium, UIHelper.Spacing.Medium + 30),
                AutoSize = true
            };

            card.Controls.AddRange(new Control[] { titleLabel, valueLabel });
            return card;
        }

        private async void UpdateStatisticsAsync()
        {
            try
            {
                var raayahCount = await _raayahRepository.CountAsync();
                var weeklyCount = await _weeklyDebtsRepository.CountAsync();

                // تحديث البطاقات (يمكن تحسينها لاحقاً)
                UpdateStatus($"الإحصائيات: {raayahCount} رعوي، {weeklyCount} سجل أسبوعي");
            }
            catch (Exception ex)
            {
                UpdateStatus($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        private void ShowRaayahManagement(object? sender, EventArgs e)
        {
            UpdateStatus("عرض إدارة الرعية...");
            var form = new RaayahListForm(_raayahRepository);
            ShowFormInPanel(form);
            UpdateStatus("تم عرض قائمة الرعية");
        }

        private void ShowWeeklyData(object? sender, EventArgs e)
        {
            UpdateStatus("عرض البيانات الأسبوعية...");
            var form = new WeeklyDataEntryForm(_raayahRepository, _weeklyDebtsRepository);
            ShowFormInPanel(form);
            UpdateStatus("تم عرض البيانات الأسبوعية");
        }

        private void ShowReports(object? sender, EventArgs e)
        {
            UpdateStatus("عرض التقارير...");
            // يمكن إضافة قائمة تقارير هنا
            ShowReportsMenu();
            UpdateStatus("تم عرض قائمة التقارير");
        }

        private void ShowReportsMenu()
        {
            mainContentPanel!.Controls.Clear();

            var reportsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = UIHelper.ModernColors.Background,
                Padding = new Padding(UIHelper.Spacing.Large)
            };

            var titleLabel = new Label
            {
                Text = "التقارير المتاحة",
                Font = UIHelper.ModernFonts.ArabicHeaderLarge,
                ForeColor = UIHelper.ModernColors.TextPrimary,
                Location = new Point(UIHelper.Spacing.Large, UIHelper.Spacing.Large),
                AutoSize = true
            };

            var buttonY = 80;
            var buttonSpacing = 50;

            var completeReportBtn = UIHelper.CreatePrimaryButton("التقرير الكامل", (s, e) => ShowCompleteReport());
            completeReportBtn.Location = new Point(UIHelper.Spacing.Large, buttonY);
            completeReportBtn.Size = new Size(200, UIHelper.Sizes.ButtonHeight);

            var discountReportBtn = UIHelper.CreateSuccessButton("تقرير خصم الحوالة", (s, e) => ShowDiscountReport());
            discountReportBtn.Location = new Point(UIHelper.Spacing.Large, buttonY + buttonSpacing);
            discountReportBtn.Size = new Size(200, UIHelper.Sizes.ButtonHeight);

            var cardsReportBtn = UIHelper.CreateSecondaryButton("كشف البطاقات", (s, e) => ShowCardsReport());
            cardsReportBtn.Location = new Point(UIHelper.Spacing.Large, buttonY + buttonSpacing * 2);
            cardsReportBtn.Size = new Size(200, UIHelper.Sizes.ButtonHeight);

            reportsPanel.Controls.AddRange(new Control[] {
                titleLabel, completeReportBtn, discountReportBtn, cardsReportBtn
            });

            mainContentPanel.Controls.Add(reportsPanel);
        }

        private void ShowTools(object? sender, EventArgs e)
        {
            UpdateStatus("عرض الأدوات...");
            var form = new BackupManagerForm();
            ShowFormInPanel(form);
            UpdateStatus("تم عرض أدوات النظام");
        }

        private void ShowSettings(object? sender, EventArgs e)
        {
            UpdateStatus("عرض الإعدادات...");
            MessageBox.Show("الإعدادات - قيد التطوير", "إعدادات النظام",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCompleteReport()
        {
            var form = new CompleteReportForm(_weeklyDebtsRepository);
            ShowFormInPanel(form);
        }

        private void ShowDiscountReport()
        {
            var form = new DiscountReportForm(_weeklyDebtsRepository);
            ShowFormInPanel(form);
        }

        private void ShowCardsReport()
        {
            var form = new CardsReportForm(_weeklyDebtsRepository);
            ShowFormInPanel(form);
        }

        private void ShowFormInPanel(Form form)
        {
            if (mainContentPanel == null) return;

            mainContentPanel.Controls.Clear();

            form.TopLevel = false;
            form.FormBorderStyle = FormBorderStyle.None;
            form.Dock = DockStyle.Fill;

            mainContentPanel.Controls.Add(form);
            form.Show();
        }

        private void UpdateStatus(string message)
        {
            if (statusLabel != null)
            {
                statusLabel.Text = message;
                statusLabel.Refresh();
            }
        }

        #endregion
    }
}
