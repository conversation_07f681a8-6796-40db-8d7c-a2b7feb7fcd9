using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using GashmiDebtManagement.UI.DesignSystem;

namespace GashmiDebtManagement.UI.Controls
{
    /// <summary>
    /// زر حديث مع تأثيرات بصرية متقدمة ودعم RTL
    /// </summary>
    public class ModernButton : Button
    {
        #region الخصائص

        private ButtonStyle _buttonStyle = ButtonStyle.Primary;
        private bool _isHovered = false;
        private bool _isPressed = false;
        private int _borderRadius = ModernDesignSystem.Dimensions.BorderRadiusMedium;
        private Color _customBackColor = Color.Empty;
        private Color _customForeColor = Color.Empty;
        private string _iconText = "";
        private bool _showRippleEffect = true;
        private Point _rippleCenter = Point.Empty;
        private int _rippleRadius = 0;
        private System.Windows.Forms.Timer _rippleTimer;

        /// <summary>
        /// نمط الزر
        /// </summary>
        [Category("Appearance")]
        [Description("نمط الزر")]
        public ButtonStyle ButtonStyle
        {
            get => _buttonStyle;
            set
            {
                _buttonStyle = value;
                UpdateColors();
                Invalidate();
            }
        }

        /// <summary>
        /// نصف قطر الحواف المدورة
        /// </summary>
        [Category("Appearance")]
        [Description("نصف قطر الحواف المدورة")]
        public int BorderRadius
        {
            get => _borderRadius;
            set
            {
                _borderRadius = Math.Max(0, value);
                Invalidate();
            }
        }

        /// <summary>
        /// نص الأيقونة
        /// </summary>
        [Category("Appearance")]
        [Description("نص الأيقونة (Emoji أو رمز)")]
        public string IconText
        {
            get => _iconText;
            set
            {
                _iconText = value ?? "";
                Invalidate();
            }
        }

        /// <summary>
        /// إظهار تأثير الموجة عند الضغط
        /// </summary>
        [Category("Behavior")]
        [Description("إظهار تأثير الموجة عند الضغط")]
        public bool ShowRippleEffect
        {
            get => _showRippleEffect;
            set => _showRippleEffect = value;
        }

        #endregion

        #region البناء والتهيئة

        public ModernButton()
        {
            InitializeComponent();
            SetStyle(ControlStyles.AllPaintingInWmPaint | 
                     ControlStyles.UserPaint | 
                     ControlStyles.DoubleBuffer | 
                     ControlStyles.ResizeRedraw, true);
            
            UpdateColors();
            
            // تهيئة مؤقت تأثير الموجة
            _rippleTimer = new System.Windows.Forms.Timer { Interval = 20 };
            _rippleTimer.Tick += RippleTimer_Tick;
        }

        private void InitializeComponent()
        {
            // إعدادات أساسية
            FlatStyle = FlatStyle.Flat;
            FlatAppearance.BorderSize = 0;
            Font = ModernDesignSystem.Typography.ButtonText;
            Size = new Size(120, ModernDesignSystem.Dimensions.ButtonHeight);
            Cursor = Cursors.Hand;
            
            // دعم RTL
            RightToLeft = RightToLeft.Yes;
            TextAlign = ContentAlignment.MiddleCenter;
        }

        #endregion

        #region إدارة الألوان

        private void UpdateColors()
        {
            var colors = GetColorsForStyle(_buttonStyle);
            _customBackColor = colors.background;
            _customForeColor = colors.foreground;
            
            BackColor = _customBackColor;
            ForeColor = _customForeColor;
        }

        private (Color background, Color foreground) GetColorsForStyle(ButtonStyle style)
        {
            return style switch
            {
                ButtonStyle.Primary => (ModernDesignSystem.Colors.Primary, ModernDesignSystem.Colors.TextOnPrimary),
                ButtonStyle.Secondary => (ModernDesignSystem.Colors.Surface, ModernDesignSystem.Colors.Primary),
                ButtonStyle.Success => (ModernDesignSystem.Colors.Success, Color.White),
                ButtonStyle.Warning => (ModernDesignSystem.Colors.Warning, Color.White),
                ButtonStyle.Error => (ModernDesignSystem.Colors.Error, Color.White),
                ButtonStyle.Outline => (Color.Transparent, ModernDesignSystem.Colors.Primary),
                ButtonStyle.Text => (Color.Transparent, ModernDesignSystem.Colors.Primary),
                _ => (ModernDesignSystem.Colors.Primary, ModernDesignSystem.Colors.TextOnPrimary)
            };
        }

        private Color GetCurrentBackColor()
        {
            if (_isPressed)
                return ControlPaint.Dark(_customBackColor, 0.2f);
            if (_isHovered)
                return ControlPaint.Light(_customBackColor, 0.1f);
            return _customBackColor;
        }

        #endregion

        #region الرسم المخصص

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;

            var rect = new Rectangle(0, 0, Width - 1, Height - 1);
            
            // رسم الخلفية
            DrawBackground(g, rect);
            
            // رسم الحدود للأزرار المحددة
            if (_buttonStyle == ButtonStyle.Outline)
                DrawBorder(g, rect);
            
            // رسم تأثير الموجة
            if (_showRippleEffect && _rippleRadius > 0)
                DrawRippleEffect(g);
            
            // رسم المحتوى
            DrawContent(g, rect);
        }

        private void DrawBackground(Graphics g, Rectangle rect)
        {
            if (_buttonStyle == ButtonStyle.Text || _buttonStyle == ButtonStyle.Outline)
                return;

            var backColor = GetCurrentBackColor();
            using var brush = new SolidBrush(backColor);
            g.FillRoundedRectangle(brush, rect, _borderRadius);
        }

        private void DrawBorder(Graphics g, Rectangle rect)
        {
            using var pen = new Pen(_customForeColor, 2);
            g.DrawRoundedRectangle(pen, rect, _borderRadius);
        }

        private void DrawRippleEffect(Graphics g)
        {
            if (_rippleRadius <= 0) return;

            using var brush = new SolidBrush(Color.FromArgb(50, Color.White));
            var rippleRect = new Rectangle(
                _rippleCenter.X - _rippleRadius,
                _rippleCenter.Y - _rippleRadius,
                _rippleRadius * 2,
                _rippleRadius * 2
            );
            
            g.FillEllipse(brush, rippleRect);
        }

        private void DrawContent(Graphics g, Rectangle rect)
        {
            var contentRect = new Rectangle(
                rect.X + ModernDesignSystem.Spacing.Small,
                rect.Y,
                rect.Width - (ModernDesignSystem.Spacing.Small * 2),
                rect.Height
            );

            // رسم الأيقونة والنص
            if (!string.IsNullOrEmpty(_iconText) && !string.IsNullOrEmpty(Text))
            {
                DrawIconAndText(g, contentRect);
            }
            else if (!string.IsNullOrEmpty(_iconText))
            {
                DrawIcon(g, contentRect);
            }
            else
            {
                DrawText(g, contentRect);
            }
        }

        private void DrawIconAndText(Graphics g, Rectangle rect)
        {
            var iconSize = (int)(Font.Size * 1.2f);
            var iconRect = new Rectangle(rect.Right - iconSize - ModernDesignSystem.Spacing.Small, 
                                       rect.Y + (rect.Height - iconSize) / 2, 
                                       iconSize, iconSize);
            
            var textRect = new Rectangle(rect.X, rect.Y, 
                                       rect.Width - iconSize - ModernDesignSystem.Spacing.Medium, 
                                       rect.Height);

            // رسم الأيقونة
            using var iconBrush = new SolidBrush(_customForeColor);
            using var iconFont = new Font(Font.FontFamily, iconSize, FontStyle.Regular);
            g.DrawString(_iconText, iconFont, iconBrush, iconRect, 
                        new StringFormat { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center });

            // رسم النص
            using var textBrush = new SolidBrush(_customForeColor);
            g.DrawString(Text, Font, textBrush, textRect, 
                        new StringFormat { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center });
        }

        private void DrawIcon(Graphics g, Rectangle rect)
        {
            using var brush = new SolidBrush(_customForeColor);
            using var iconFont = new Font(Font.FontFamily, Font.Size * 1.2f, FontStyle.Regular);
            g.DrawString(_iconText, iconFont, brush, rect, 
                        new StringFormat { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center });
        }

        private void DrawText(Graphics g, Rectangle rect)
        {
            using var brush = new SolidBrush(_customForeColor);
            g.DrawString(Text, Font, brush, rect, 
                        new StringFormat { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center });
        }

        #endregion

        #region معالجة الأحداث

        protected override void OnMouseEnter(EventArgs e)
        {
            _isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            _isHovered = false;
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            _isPressed = true;
            
            if (_showRippleEffect)
            {
                _rippleCenter = e.Location;
                _rippleRadius = 0;
                _rippleTimer.Start();
            }
            
            Invalidate();
            base.OnMouseDown(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            _isPressed = false;
            Invalidate();
            base.OnMouseUp(e);
        }

        private void RippleTimer_Tick(object sender, EventArgs e)
        {
            _rippleRadius += 5;
            
            if (_rippleRadius > Math.Max(Width, Height))
            {
                _rippleTimer.Stop();
                _rippleRadius = 0;
            }
            
            Invalidate();
        }

        #endregion

        #region تنظيف الموارد

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _rippleTimer?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }

    /// <summary>
    /// أنماط الأزرار المتاحة
    /// </summary>
    public enum ButtonStyle
    {
        Primary,    // أساسي
        Secondary,  // ثانوي
        Success,    // نجاح
        Warning,    // تحذير
        Error,      // خطأ
        Outline,    // محدد
        Text        // نصي
    }
}
