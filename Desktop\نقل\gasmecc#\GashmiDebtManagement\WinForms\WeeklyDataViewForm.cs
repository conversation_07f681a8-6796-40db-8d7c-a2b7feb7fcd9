using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Services;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج عرض البيانات الأسبوعية
    /// </summary>
    public partial class WeeklyDataViewForm : Form
    {
        private readonly IWeeklyDebtsRepository _weeklyDebtsRepository;
        
        // عناصر التحكم
        private Panel? filterPanel;
        private Label? fromDateLabel;
        private DateTimePicker? fromDatePicker;
        private Label? toDateLabel;
        private DateTimePicker? toDatePicker;
        private Button? loadDataButton;
        private Button? exportButton;
        private DataGridView? dataGridView;
        private Panel? summaryPanel;
        private Label? summaryLabel;
        private Label? recordCountLabel;

        public WeeklyDataViewForm(IWeeklyDebtsRepository weeklyDebtsRepository)
        {
            _weeklyDebtsRepository = weeklyDebtsRepository;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "عرض البيانات الأسبوعية";
            this.Size = new Size(1400, 700);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);

            // إنشاء لوحة التصفية
            CreateFilterPanel();

            // إنشاء جدول البيانات
            CreateDataGridView();

            // إنشاء لوحة الملخص
            CreateSummaryPanel();

            this.ResumeLayout(false);
        }

        private void CreateFilterPanel()
        {
            filterPanel = new Panel();
            filterPanel.Height = 80;
            filterPanel.Dock = DockStyle.Top;
            filterPanel.BackColor = Color.LightBlue;

            // تسمية تاريخ البداية
            fromDateLabel = new Label();
            fromDateLabel.Text = "من تاريخ:";
            fromDateLabel.Location = new Point(1200, 25);
            fromDateLabel.Size = new Size(80, 23);
            fromDateLabel.TextAlign = ContentAlignment.MiddleRight;

            // منتقي تاريخ البداية
            fromDatePicker = new DateTimePicker();
            fromDatePicker.Location = new Point(1050, 25);
            fromDatePicker.Size = new Size(140, 23);
            fromDatePicker.Format = DateTimePickerFormat.Short;
            fromDatePicker.Value = DateTime.Now.AddDays(-7);

            // تسمية تاريخ النهاية
            toDateLabel = new Label();
            toDateLabel.Text = "إلى تاريخ:";
            toDateLabel.Location = new Point(950, 25);
            toDateLabel.Size = new Size(80, 23);
            toDateLabel.TextAlign = ContentAlignment.MiddleRight;

            // منتقي تاريخ النهاية
            toDatePicker = new DateTimePicker();
            toDatePicker.Location = new Point(800, 25);
            toDatePicker.Size = new Size(140, 23);
            toDatePicker.Format = DateTimePickerFormat.Short;
            toDatePicker.Value = DateTime.Now;

            // زر تحميل البيانات
            loadDataButton = new Button();
            loadDataButton.Text = "تحميل البيانات";
            loadDataButton.Size = new Size(120, 35);
            loadDataButton.Location = new Point(650, 20);
            loadDataButton.BackColor = Color.LightGreen;
            loadDataButton.Click += LoadDataButton_Click;

            // زر التصدير
            exportButton = new Button();
            exportButton.Text = "تصدير CSV";
            exportButton.Size = new Size(100, 35);
            exportButton.Location = new Point(540, 20);
            exportButton.BackColor = Color.LightYellow;
            exportButton.Enabled = false;
            exportButton.Click += ExportButton_Click;

            filterPanel.Controls.AddRange(new Control[] {
                fromDateLabel, fromDatePicker, toDateLabel, toDatePicker,
                loadDataButton, exportButton
            });

            this.Controls.Add(filterPanel);
        }

        private void CreateDataGridView()
        {
            dataGridView = new DataGridView();
            dataGridView.Dock = DockStyle.Fill;
            dataGridView.AutoGenerateColumns = false;
            dataGridView.ReadOnly = true;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.BackgroundColor = Color.White;
            dataGridView.Font = new Font("Tahoma", 9F);

            // إعداد الأعمدة
            SetupDataGridViewColumns();

            this.Controls.Add(dataGridView);
        }

        private void SetupDataGridViewColumns()
        {
            var nameColumn = new DataGridViewTextBoxColumn();
            nameColumn.Name = "RaayahName";
            nameColumn.HeaderText = "اسم الرعوي";
            nameColumn.DataPropertyName = "Raayah.FullName";
            nameColumn.Width = 200;

            var periodColumn = new DataGridViewTextBoxColumn();
            periodColumn.Name = "Period";
            periodColumn.HeaderText = "الفترة";
            periodColumn.Width = 150;

            var samirColumn = new DataGridViewTextBoxColumn();
            samirColumn.Name = "SamirAmount";
            samirColumn.HeaderText = "سمير";
            samirColumn.DataPropertyName = "SamirAmount";
            samirColumn.Width = 100;
            samirColumn.DefaultCellStyle.Format = "N2";

            var maherColumn = new DataGridViewTextBoxColumn();
            maherColumn.Name = "MaherAmount";
            maherColumn.HeaderText = "ماهر";
            maherColumn.DataPropertyName = "MaherAmount";
            maherColumn.Width = 100;
            maherColumn.DefaultCellStyle.Format = "N2";

            var raidColumn = new DataGridViewTextBoxColumn();
            raidColumn.Name = "RaidAmount";
            raidColumn.HeaderText = "رايد";
            raidColumn.DataPropertyName = "RaidAmount";
            raidColumn.Width = 100;
            raidColumn.DefaultCellStyle.Format = "N2";

            var haiderColumn = new DataGridViewTextBoxColumn();
            haiderColumn.Name = "HaiderAmount";
            haiderColumn.HeaderText = "حيدر";
            haiderColumn.DataPropertyName = "HaiderAmount";
            haiderColumn.Width = 100;
            haiderColumn.DefaultCellStyle.Format = "N2";

            var lateColumn = new DataGridViewTextBoxColumn();
            lateColumn.Name = "LateAmount";
            lateColumn.HeaderText = "متأخر";
            lateColumn.DataPropertyName = "LateAmount";
            lateColumn.Width = 100;
            lateColumn.DefaultCellStyle.Format = "N2";

            var totalColumn = new DataGridViewTextBoxColumn();
            totalColumn.Name = "TotalDebts";
            totalColumn.HeaderText = "إجمالي الديون";
            totalColumn.DataPropertyName = "TotalDebtsAmount";
            totalColumn.Width = 120;
            totalColumn.DefaultCellStyle.Format = "N2";
            totalColumn.DefaultCellStyle.BackColor = Color.LightYellow;

            var receivedColumn = new DataGridViewTextBoxColumn();
            receivedColumn.Name = "ReceivedAmount";
            receivedColumn.HeaderText = "الواصل";
            receivedColumn.DataPropertyName = "ReceivedAmount";
            receivedColumn.Width = 100;
            receivedColumn.DefaultCellStyle.Format = "N2";

            var discountColumn = new DataGridViewTextBoxColumn();
            discountColumn.Name = "DiscountAmount";
            discountColumn.HeaderText = "الخصم";
            discountColumn.DataPropertyName = "DiscountAmount";
            discountColumn.Width = 100;
            discountColumn.DefaultCellStyle.Format = "N2";
            discountColumn.DefaultCellStyle.BackColor = Color.LightCoral;

            var netColumn = new DataGridViewTextBoxColumn();
            netColumn.Name = "NetAmount";
            netColumn.HeaderText = "الصافي";
            netColumn.DataPropertyName = "NetAmount";
            netColumn.Width = 120;
            netColumn.DefaultCellStyle.Format = "N2";
            netColumn.DefaultCellStyle.BackColor = Color.LightGreen;

            dataGridView!.Columns.AddRange(new DataGridViewColumn[] {
                nameColumn, periodColumn, samirColumn, maherColumn, raidColumn,
                haiderColumn, lateColumn, totalColumn, receivedColumn, discountColumn, netColumn
            });

            // تنسيق الرؤوس
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Navy;
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            dataGridView.ColumnHeadersHeight = 35;

            // تنسيق الصفوف
            dataGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.AliceBlue;
            dataGridView.RowsDefaultCellStyle.BackColor = Color.White;
            dataGridView.RowTemplate.Height = 30;

            // حدث تنسيق الخلايا
            dataGridView.CellFormatting += DataGridView_CellFormatting;
        }

        private void CreateSummaryPanel()
        {
            summaryPanel = new Panel();
            summaryPanel.Height = 80;
            summaryPanel.Dock = DockStyle.Bottom;
            summaryPanel.BackColor = Color.LightGray;

            // تسمية الملخص
            summaryLabel = new Label();
            summaryLabel.Text = "الملخص: لم يتم تحميل البيانات بعد";
            summaryLabel.Location = new Point(20, 10);
            summaryLabel.Size = new Size(1000, 30);
            summaryLabel.Font = new Font("Tahoma", 11F, FontStyle.Bold);

            // تسمية عدد السجلات
            recordCountLabel = new Label();
            recordCountLabel.Text = "عدد السجلات: 0";
            recordCountLabel.Location = new Point(20, 45);
            recordCountLabel.Size = new Size(200, 25);
            recordCountLabel.Font = new Font("Tahoma", 10F);

            summaryPanel.Controls.AddRange(new Control[] {
                summaryLabel, recordCountLabel
            });

            this.Controls.Add(summaryPanel);
        }

        private async void LoadDataButton_Click(object? sender, EventArgs e)
        {
            try
            {
                loadDataButton!.Enabled = false;
                loadDataButton.Text = "جاري التحميل...";

                var fromDate = fromDatePicker!.Value.Date;
                var toDate = toDatePicker!.Value.Date;

                if (fromDate > toDate)
                {
                    MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية", 
                        "خطأ في التاريخ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // تحميل البيانات
                var weeklyData = await _weeklyDebtsRepository.GetByPeriodWithRaayahAsync(fromDate, toDate);
                var dataList = weeklyData.OrderBy(d => d.Raayah.FullName).ThenBy(d => d.DateFrom).ToList();

                // عرض البيانات
                dataGridView!.DataSource = dataList;

                // تحديث الملخص
                UpdateSummary(dataList);

                // تفعيل زر التصدير
                exportButton!.Enabled = dataList.Any();

                // تحديث عدد السجلات
                recordCountLabel!.Text = $"عدد السجلات: {dataList.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                loadDataButton!.Enabled = true;
                loadDataButton.Text = "تحميل البيانات";
            }
        }

        private void DataGridView_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.RowIndex >= 0 && dataGridView!.Columns[e.ColumnIndex].Name == "Period")
            {
                var debt = dataGridView.Rows[e.RowIndex].DataBoundItem as WeeklyDebts;
                if (debt != null)
                {
                    e.Value = $"{debt.DateFrom:dd/MM/yyyy} - {debt.DateTo:dd/MM/yyyy}";
                    e.FormattingApplied = true;
                }
            }
        }

        private void UpdateSummary(List<WeeklyDebts> data)
        {
            if (!data.Any())
            {
                summaryLabel!.Text = "الملخص: لا توجد بيانات للفترة المحددة";
                return;
            }

            var totals = CalculationService.CalculatePeriodTotals(data);
            var branches = CalculationService.CalculateBranchesTotals(data);

            summaryLabel!.Text = $"الملخص - إجمالي الديون: {totals.TotalDebts:N2} | " +
                                $"إجمالي الواصل: {totals.TotalReceived:N2} | " +
                                $"إجمالي الخصومات: {totals.TotalDiscount:N2} | " +
                                $"الصافي: {totals.TotalNet:N2}";
        }

        private void ExportButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var data = dataGridView!.DataSource as List<WeeklyDebts>;
                if (data == null || !data.Any())
                {
                    MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var fromDate = fromDatePicker!.Value.Date;
                var toDate = toDatePicker!.Value.Date;

                var filePath = ExportService.ExportCompleteReportToCsv(data, fromDate, toDate);

                var result = MessageBox.Show($"تم تصدير البيانات بنجاح إلى:\n{filePath}\n\nهل تريد فتح الملف؟", 
                    "تم التصدير", MessageBoxButtons.YesNo, MessageBoxIcon.Information);

                if (result == DialogResult.Yes)
                {
                    System.Diagnostics.Process.Start("notepad.exe", filePath);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في تصدير البيانات:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            
            // تحميل البيانات تلقائياً للأسبوع الحالي
            LoadDataButton_Click(null, EventArgs.Empty);
        }
    }
}
