@echo off
chcp 65001 > nul
title تنظيف نظام إدارة ديون الرعية

echo ========================================
echo    تنظيف نظام إدارة ديون الرعية
echo    شركة الغشمي
echo ========================================
echo.

echo تحذير: سيتم حذف الملفات المؤقتة والمبنية
echo هل تريد المتابعة؟ (y/n)
set /p confirm=

if /i "%confirm%" NEQ "y" (
    echo تم إلغاء العملية
    pause
    exit /b 0
)

echo.
echo جاري تنظيف المشروع...
echo.

REM تنظيف ملفات البناء
if exist "bin" (
    rmdir /s /q bin
    echo ✅ تم حذف مجلد bin
)

if exist "obj" (
    rmdir /s /q obj
    echo ✅ تم حذف مجلد obj
)

if exist "publish" (
    rmdir /s /q publish
    echo ✅ تم حذف مجلد publish
)

REM تنظيف ملفات مؤقتة
if exist "*.tmp" (
    del /q *.tmp
    echo ✅ تم حذف الملفات المؤقتة
)

if exist "*.log" (
    del /q *.log
    echo ✅ تم حذف ملفات السجلات
)

REM تنظيف مجلد السجلات
if exist "Logs\*.log" (
    del /q Logs\*.log
    echo ✅ تم تنظيف مجلد السجلات
)

echo.
echo هل تريد حذف النسخ الاحتياطية القديمة؟ (y/n)
set /p cleanBackups=

if /i "%cleanBackups%" EQU "y" (
    if exist "Backups\*.zip" (
        del /q Backups\*.zip
        echo ✅ تم حذف النسخ الاحتياطية
    )
)

echo.
echo هل تريد حذف الملفات المصدرة القديمة؟ (y/n)
set /p cleanExports=

if /i "%cleanExports%" EQU "y" (
    if exist "Exports\*.csv" (
        del /q Exports\*.csv
        echo ✅ تم حذف الملفات المصدرة
    )
)

echo.
echo جاري تنظيف .NET...
dotnet clean
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم تنظيف .NET بنجاح
) else (
    echo ⚠️ تحذير: فشل في تنظيف .NET
)

echo.
echo ========================================
echo    تم تنظيف المشروع بنجاح!
echo ========================================
echo.
echo لإعادة بناء المشروع:
echo 🔨 انقر نقراً مزدوجاً على build.bat
echo أو استخدم الأمر: dotnet build
echo.

pause
