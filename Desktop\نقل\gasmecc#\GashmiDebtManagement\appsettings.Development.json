{"ConnectionStrings": {"DefaultConnection": "Data Source=Data/GashmiDebtManagement_Dev.db;Cache=Shared"}, "AppSettings": {"AutoBackupOnStartup": false, "DatabaseTimeout": 10}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information", "GashmiDebtManagement": "Debug"}, "LogToConsole": true, "LogToFile": true}, "Performance": {"CacheEnabled": false, "EnableLazyLoading": false}, "Features": {"EnableSystemTests": true}, "Development": {"SeedTestData": true, "EnableDetailedErrors": true, "ShowSqlQueries": true, "EnableHotReload": true}}