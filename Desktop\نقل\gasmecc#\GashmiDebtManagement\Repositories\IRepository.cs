using System.Linq.Expressions;

namespace GashmiDebtManagement.Repositories
{
    /// <summary>
    /// واجهة Repository العامة
    /// </summary>
    /// <typeparam name="T">نوع الكيان</typeparam>
    public interface IRepository<T> where T : class
    {
        // العمليات الأساسية
        Task<T?> GetByIdAsync(int id);
        Task<IEnumerable<T>> GetAllAsync();
        Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);
        Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate);
        
        // عمليات الإضافة والتعديل والحذف
        Task<T> AddAsync(T entity);
        Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities);
        Task UpdateAsync(T entity);
        Task UpdateRangeAsync(IEnumerable<T> entities);
        Task DeleteAsync(T entity);
        Task DeleteRangeAsync(IEnumerable<T> entities);
        Task DeleteByIdAsync(int id);
        
        // عمليات العد والتحقق
        Task<int> CountAsync();
        Task<int> CountAsync(Expression<Func<T, bool>> predicate);
        Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);
        
        // حفظ التغييرات
        Task<int> SaveChangesAsync();
    }
}
