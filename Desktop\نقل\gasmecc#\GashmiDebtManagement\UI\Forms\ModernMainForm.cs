using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using GashmiDebtManagement.UI.DesignSystem;
using GashmiDebtManagement.UI.Controls;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Services;
using GashmiDebtManagement.Helpers;

namespace GashmiDebtManagement.UI.Forms
{
    /// <summary>
    /// النافذة الرئيسية المتقدمة مع تصميم Material Design
    /// </summary>
    public partial class ModernMainForm : Form
    {
        #region المتغيرات الخاصة

        private ApplicationDbContext _context = null!;
        private IRaayahRepository _raayahRepository = null!;
        private IWeeklyDebtsRepository _weeklyDebtsRepository = null!;

        // عناصر التخطيط الرئيسية
        private Panel _headerPanel = null!;
        private Panel _sidebarPanel = null!;
        private Panel _contentPanel = null!;
        private Panel _statusPanel = null!;

        // عناصر الرأس
        private Label _titleLabel = null!;
        private Label _userLabel = null!;
        private ModernButton _notificationButton = null!;
        private ModernButton _settingsButton = null!;

        // عناصر الشريط الجانبي
        private ModernButton _dashboardButton = null!;
        private ModernButton _raayahButton = null!;
        private ModernButton _weeklyDataButton = null!;
        private ModernButton _reportsButton = null!;
        private ModernButton _toolsButton = null!;

        // عناصر شريط الحالة
        private Label _statusLabel = null!;
        private Label _timeLabel = null!;
        private System.Windows.Forms.Timer _timeTimer = null!;

        // المحتوى الحالي
        private UserControl? _currentContent;

        #endregion

        #region البناء والتهيئة

        public ModernMainForm()
        {
            try
            {
                InitializeComponent();
                InitializeDatabase();
                InitializeModernDesign();
                CreateModernLayout();
                LoadDashboard();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة الرئيسية:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeComponent()
        {
            SuspendLayout();

            // إعدادات النافذة
            Text = "نظام إدارة ديون الرعية - شركة الغشمي";
            Size = new Size(1400, 900);
            StartPosition = FormStartPosition.CenterScreen;
            WindowState = FormWindowState.Maximized;
            MinimumSize = new Size(1200, 700);
            Icon = SystemIcons.Application;
            BackColor = ModernDesignSystem.Colors.Background;

            // دعم RTL
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;

            ResumeLayout(false);
            PerformLayout();
        }

        private void InitializeDatabase()
        {
            _context = new ApplicationDbContext();
            _raayahRepository = new RaayahRepository(_context);
            _weeklyDebtsRepository = new WeeklyDebtsRepository(_context);

            // التأكد من إنشاء قاعدة البيانات
            _context.EnsureCreated();

            // نسخة احتياطية تلقائية
            BackupService.AutoBackupOnStartup();
        }

        private void InitializeModernDesign()
        {
            // تنظيف أي عناصر قديمة
            CleanupOldControls();

            // تطبيق دعم RTL المحسن
            RTLHelper.ApplyRTL(this);

            // إضافة اختصارات لوحة المفاتيح
            UIHelper.AddKeyboardShortcuts(this);

            // تهيئة مؤقت الوقت
            _timeTimer = new System.Windows.Forms.Timer { Interval = 1000 };
            _timeTimer.Tick += TimeTimer_Tick;
            _timeTimer.Start();
        }

        private void CleanupOldControls()
        {
            // إزالة أي عناصر تحكم قديمة قد تكون متبقية
            var controlsToRemove = new List<Control>();

            foreach (Control control in Controls)
            {
                // البحث عن عناصر قديمة بأسماء معينة أو أنواع قديمة
                if (control.Name?.Contains("old") == true ||
                    control.Name?.Contains("legacy") == true ||
                    control.GetType().Name.Contains("Old") ||
                    control.GetType().Name.Contains("Legacy") ||
                    // إزالة أي Labels أو Buttons قديمة غير مرغوب فيها
                    (control is Label label && string.IsNullOrEmpty(label.Name)) ||
                    (control is Button button && string.IsNullOrEmpty(button.Name)))
                {
                    controlsToRemove.Add(control);
                }
            }

            foreach (var control in controlsToRemove)
            {
                Controls.Remove(control);
                control.Dispose();
            }

            // تنظيف إضافي: إزالة أي نصوص عائمة
            Invalidate(true);
            Update();
        }

        private void CleanupSidebarOldElements()
        {
            // التأكد من عدم وجود عناصر قديمة في الشريط الجانبي
            var elementsToRemove = new List<Control>();

            foreach (Control control in _sidebarPanel.Controls)
            {
                // إزالة أي عناصر غير مرغوب فيها
                if (!(control is ModernButton) && !(control is Label) && !(control is Panel))
                {
                    elementsToRemove.Add(control);
                }
            }

            foreach (var element in elementsToRemove)
            {
                _sidebarPanel.Controls.Remove(element);
                element.Dispose();
            }
        }

        #endregion

        #region إنشاء التخطيط

        private void CreateModernLayout()
        {
            // مسح أي عناصر موجودة مسبقاً
            Controls.Clear();

            // ترتيب إنشاء العناصر من الأسفل للأعلى
            CreateStatusPanel();      // الأسفل
            CreateHeaderPanel();      // الأعلى
            CreateSidebarPanel();     // اليمين
            CreateContentPanel();     // الوسط (يملأ المساحة المتبقية)

            // ترتيب الطبقات بشكل صحيح
            _contentPanel.SendToBack();     // المحتوى في الخلف
            _sidebarPanel.BringToFront();   // الشريط الجانبي
            _headerPanel.BringToFront();    // الرأس في المقدمة
            _statusPanel.BringToFront();    // شريط الحالة في المقدمة

            // التأكد من الترتيب
            this.Refresh();
        }

        private void CreateHeaderPanel()
        {
            _headerPanel = new Panel
            {
                Height = ModernDesignSystem.Dimensions.HeaderHeight,
                Dock = DockStyle.Top,
                BackColor = ModernDesignSystem.Colors.Primary,
                Padding = new Padding(ModernDesignSystem.Spacing.Large)
            };

            // عنوان التطبيق
            _titleLabel = new Label
            {
                Text = "نظام إدارة ديون الرعية",
                Font = ModernDesignSystem.Typography.Headline,
                ForeColor = ModernDesignSystem.Colors.TextOnPrimary,
                AutoSize = true,
                Location = new Point(ModernDesignSystem.Spacing.Large, 
                    (_headerPanel.Height - (int)ModernDesignSystem.Typography.Headline.Size) / 2)
            };

            // معلومات المستخدم
            _userLabel = new Label
            {
                Text = "شركة الغشمي",
                Font = ModernDesignSystem.Typography.Body,
                ForeColor = ModernDesignSystem.Colors.TextOnPrimary,
                AutoSize = true
            };

            // زر الإشعارات
            _notificationButton = new ModernButton
            {
                IconText = "🔔",
                Size = new Size(40, 40),
                ButtonStyle = ButtonStyle.Text,
                BorderRadius = 20
            };

            // زر الإعدادات
            _settingsButton = new ModernButton
            {
                IconText = "⚙️",
                Size = new Size(40, 40),
                ButtonStyle = ButtonStyle.Text,
                BorderRadius = 20
            };

            // ترتيب العناصر
            var rightX = _headerPanel.Width - ModernDesignSystem.Spacing.Large;
            _settingsButton.Location = new Point(rightX - _settingsButton.Width, 
                (_headerPanel.Height - _settingsButton.Height) / 2);
            
            _notificationButton.Location = new Point(_settingsButton.Left - _notificationButton.Width - ModernDesignSystem.Spacing.Small,
                (_headerPanel.Height - _notificationButton.Height) / 2);

            _userLabel.Location = new Point(_notificationButton.Left - _userLabel.Width - ModernDesignSystem.Spacing.Large,
                (_headerPanel.Height - _userLabel.Height) / 2);

            _headerPanel.Controls.AddRange(new Control[] { 
                _titleLabel, _userLabel, _notificationButton, _settingsButton 
            });

            Controls.Add(_headerPanel);
            _headerPanel.BringToFront();
        }

        private void CreateSidebarPanel()
        {
            _sidebarPanel = new Panel
            {
                Width = ModernDesignSystem.Dimensions.SidebarWidth,
                Dock = DockStyle.Right,
                BackColor = ModernDesignSystem.Colors.Surface,
                Padding = new Padding(ModernDesignSystem.Spacing.Medium),
                RightToLeft = RightToLeft.Yes
            };

            // إضافة ظل للشريط الجانبي (من الجهة اليسرى للعربية)
            _sidebarPanel.Paint += (s, e) => {
                var shadowRect = new Rectangle(-8, 0, 8, _sidebarPanel.Height);
                using var shadowBrush = new SolidBrush(ModernDesignSystem.Shadows.Shadow2);
                e.Graphics.FillRectangle(shadowBrush, shadowRect);
            };

            CreateSidebarButtons();

            // تنظيف أي عناصر قديمة في الشريط الجانبي
            CleanupSidebarOldElements();

            Controls.Add(_sidebarPanel);
        }

        private void CreateSidebarButtons()
        {
            // إضافة عنوان للشريط الجانبي
            var titleLabel = new Label
            {
                Text = "القائمة الرئيسية",
                Font = ModernDesignSystem.Typography.Subheading,
                ForeColor = ModernDesignSystem.Colors.TextPrimary,
                Size = new Size(_sidebarPanel.Width - (ModernDesignSystem.Spacing.Medium * 2), 30),
                Location = new Point(ModernDesignSystem.Spacing.Medium, ModernDesignSystem.Spacing.Medium),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };
            _sidebarPanel.Controls.Add(titleLabel);

            // إضافة خط فاصل
            var separator = new Panel
            {
                Height = 1,
                Width = _sidebarPanel.Width - (ModernDesignSystem.Spacing.Medium * 2),
                Location = new Point(ModernDesignSystem.Spacing.Medium, titleLabel.Bottom + ModernDesignSystem.Spacing.Small),
                BackColor = ModernDesignSystem.Colors.Divider
            };
            _sidebarPanel.Controls.Add(separator);

            var buttonHeight = ModernDesignSystem.Dimensions.ButtonHeight;
            var buttonWidth = _sidebarPanel.Width - (ModernDesignSystem.Spacing.Medium * 2);
            var spacing = ModernDesignSystem.Spacing.Medium;
            var currentY = separator.Bottom + ModernDesignSystem.Spacing.Large;

            // زر لوحة المعلومات
            _dashboardButton = CreateSidebarButton("📊 لوحة المعلومات", currentY, buttonWidth, buttonHeight, (s, e) => LoadDashboard());
            _dashboardButton.ButtonStyle = ButtonStyle.Primary; // الافتراضي المحدد
            currentY += buttonHeight + spacing;

            // زر إدارة الرعية
            _raayahButton = CreateSidebarButton("👥 إدارة الرعية", currentY, buttonWidth, buttonHeight, (s, e) => LoadRaayahManagement());
            currentY += buttonHeight + spacing;

            // زر البيانات الأسبوعية
            _weeklyDataButton = CreateSidebarButton("📅 البيانات الأسبوعية", currentY, buttonWidth, buttonHeight, (s, e) => LoadWeeklyData());
            currentY += buttonHeight + spacing;

            // زر التقارير
            _reportsButton = CreateSidebarButton("📈 التقارير", currentY, buttonWidth, buttonHeight, (s, e) => LoadReports());
            currentY += buttonHeight + spacing;

            // زر الأدوات
            _toolsButton = CreateSidebarButton("🔧 الأدوات", currentY, buttonWidth, buttonHeight, (s, e) => LoadTools());

            _sidebarPanel.Controls.AddRange(new Control[] {
                _dashboardButton, _raayahButton, _weeklyDataButton, _reportsButton, _toolsButton
            });
        }

        private ModernButton CreateSidebarButton(string text, int y, int width, int height, EventHandler? clickHandler)
        {
            var button = new ModernButton
            {
                Text = text,
                Size = new Size(width, height),
                Location = new Point(ModernDesignSystem.Spacing.Medium, y),
                ButtonStyle = ButtonStyle.Text,
                BorderRadius = ModernDesignSystem.Dimensions.BorderRadiusMedium,
                TextAlign = ContentAlignment.MiddleRight, // محاذاة النص لليمين للعربية
                RightToLeft = RightToLeft.Yes,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right,
                FlatStyle = FlatStyle.Flat
            };

            // إزالة الحدود الافتراضية
            button.FlatAppearance.BorderSize = 0;
            // تجنب استخدام Transparent مع BorderColor
            button.FlatAppearance.BorderColor = button.BackColor;

            button.Click += (s, e) => {
                SelectSidebarButton(button);
                clickHandler?.Invoke(s, e);
            };

            return button;
        }

        private void CreateContentPanel()
        {
            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = ModernDesignSystem.Colors.Background,
                Padding = new Padding(ModernDesignSystem.Spacing.Large),
                AutoScroll = true
            };

            Controls.Add(_contentPanel);
            _contentPanel.SendToBack();
        }

        private void CreateStatusPanel()
        {
            _statusPanel = new Panel
            {
                Height = ModernDesignSystem.Dimensions.StatusBarHeight + ModernDesignSystem.Spacing.Small,
                Dock = DockStyle.Bottom,
                BackColor = ModernDesignSystem.Colors.Surface,
                Padding = new Padding(ModernDesignSystem.Spacing.Medium, ModernDesignSystem.Spacing.XSmall, ModernDesignSystem.Spacing.Medium, ModernDesignSystem.Spacing.XSmall)
            };

            _statusLabel = new Label
            {
                Text = "جاهز",
                Font = ModernDesignSystem.Typography.Caption,
                ForeColor = ModernDesignSystem.Colors.TextSecondary,
                AutoSize = true,
                Location = new Point(ModernDesignSystem.Spacing.Medium, ModernDesignSystem.Spacing.XSmall)
            };

            _timeLabel = new Label
            {
                Font = ModernDesignSystem.Typography.Caption,
                ForeColor = ModernDesignSystem.Colors.TextSecondary,
                AutoSize = true
            };

            UpdateTimeLabel();

            _statusPanel.Controls.AddRange(new Control[] { _statusLabel, _timeLabel });
            Controls.Add(_statusPanel);
        }

        #endregion

        #region إدارة المحتوى

        private void LoadContent(UserControl content)
        {
            try
            {
                if (_currentContent != null)
                {
                    _contentPanel.Controls.Remove(_currentContent);
                    _currentContent.Dispose();
                }

                _currentContent = content;
                if (content != null)
                {
                    content.Dock = DockStyle.Fill;
                    _contentPanel.Controls.Add(content);
                    content.BringToFront();
                    _contentPanel.Refresh();

                    UpdateStatus($"تم تحميل المحتوى: {content.GetType().Name}");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"خطأ في تحميل المحتوى: {ex.Message}");
            }
        }

        private void SelectSidebarButton(ModernButton selectedButton)
        {
            // إلغاء تحديد جميع الأزرار
            foreach (var button in _sidebarPanel.Controls.OfType<ModernButton>())
            {
                button.ButtonStyle = ButtonStyle.Text;
            }

            // تحديد الزر المختار
            selectedButton.ButtonStyle = ButtonStyle.Primary;
        }

        #endregion

        #region معالجات الأحداث

        private void LoadDashboard()
        {
            try
            {
                UpdateStatus("تحميل لوحة المعلومات...");
                var dashboard = CreateDashboardContent();
                LoadContent(dashboard);
                UpdateStatus("تم تحميل لوحة المعلومات");
            }
            catch (Exception ex)
            {
                UpdateStatus($"خطأ في تحميل لوحة المعلومات: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل لوحة المعلومات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private UserControl CreateDashboardContent()
        {
            var userControl = new UserControl
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                BackColor = ModernDesignSystem.Colors.Background,
                RightToLeft = RightToLeft.Yes
            };

            // إضافة بطاقات إحصائية مؤقتة
            var welcomeCard = ModernCard.CreateSimpleCard(
                "مرحباً بك في نظام إدارة ديون الرعية",
                "شركة الغشمي للتجارة والمقاولات",
                "🏢"
            );
            welcomeCard.Location = new Point(20, 20);
            welcomeCard.Size = new Size(400, 150);
            userControl.Controls.Add(welcomeCard);

            // إضافة بطاقة إضافية للاختبار
            var statsCard = ModernCard.CreateSimpleCard(
                "إحصائيات سريعة",
                "عدد الرعية: 0 | إجمالي الديون: 0",
                "📊"
            );
            statsCard.Location = new Point(20, 190);
            statsCard.Size = new Size(400, 120);
            userControl.Controls.Add(statsCard);

            return userControl;
        }

        private void LoadRaayahManagement()
        {
            UpdateStatus("تحميل إدارة الرعية...");
            var placeholder = CreatePlaceholderContent("إدارة الرعية", "قريباً...", "👥");
            LoadContent(placeholder);
            UpdateStatus("تم تحميل إدارة الرعية");
        }

        private void LoadWeeklyData()
        {
            UpdateStatus("تحميل البيانات الأسبوعية...");
            var placeholder = CreatePlaceholderContent("البيانات الأسبوعية", "قريباً...", "📅");
            LoadContent(placeholder);
            UpdateStatus("تم تحميل البيانات الأسبوعية");
        }

        private void LoadReports()
        {
            UpdateStatus("تحميل التقارير...");
            var placeholder = CreatePlaceholderContent("التقارير", "قريباً...", "📈");
            LoadContent(placeholder);
            UpdateStatus("تم تحميل التقارير");
        }

        private void LoadTools()
        {
            UpdateStatus("تحميل الأدوات...");
            var placeholder = CreatePlaceholderContent("الأدوات", "قريباً...", "🔧");
            LoadContent(placeholder);
            UpdateStatus("تم تحميل الأدوات");
        }

        private UserControl CreatePlaceholderContent(string title, string subtitle, string icon)
        {
            var userControl = new UserControl { Dock = DockStyle.Fill };
            var card = ModernCard.CreateSimpleCard(title, subtitle, icon);
            card.Location = new Point(20, 20);
            userControl.Controls.Add(card);
            return userControl;
        }

        private void TimeTimer_Tick(object sender, EventArgs e)
        {
            UpdateTimeLabel();
        }

        #endregion

        #region دوال مساعدة

        private void UpdateStatus(string message)
        {
            if (_statusLabel != null)
            {
                _statusLabel.Text = message;
            }
        }

        private void UpdateTimeLabel()
        {
            if (_timeLabel != null)
            {
                _timeLabel.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss");
                _timeLabel.Location = new Point(
                    _statusPanel.Width - _timeLabel.Width - ModernDesignSystem.Spacing.Medium,
                    ModernDesignSystem.Spacing.XSmall
                );
            }
        }

        #endregion

        #region تنظيف الموارد

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _timeTimer?.Dispose();
                _currentContent?.Dispose();
                _context?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
