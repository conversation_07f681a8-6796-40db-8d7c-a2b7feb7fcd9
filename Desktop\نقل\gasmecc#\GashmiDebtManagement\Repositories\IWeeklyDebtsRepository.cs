using GashmiDebtManagement.Models;

namespace GashmiDebtManagement.Repositories
{
    /// <summary>
    /// واجهة Repository خاصة بالديون الأسبوعية
    /// </summary>
    public interface IWeeklyDebtsRepository : IRepository<WeeklyDebts>
    {
        /// <summary>
        /// الحصول على ديون فترة معينة
        /// </summary>
        Task<IEnumerable<WeeklyDebts>> GetByPeriodAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// الحصول على ديون رعوي معين لفترة معينة
        /// </summary>
        Task<IEnumerable<WeeklyDebts>> GetByRaayahAndPeriodAsync(int raayahId, DateTime fromDate, DateTime toDate);

        /// <summary>
        /// الحصول على ديون رعوي معين
        /// </summary>
        Task<IEnumerable<WeeklyDebts>> GetByRaayahAsync(int raayahId);

        /// <summary>
        /// الحصول على الديون مع بيانات الرعية
        /// </summary>
        Task<IEnumerable<WeeklyDebts>> GetWithRaayahAsync();

        /// <summary>
        /// الحصول على ديون فترة معينة مع بيانات الرعية
        /// </summary>
        Task<IEnumerable<WeeklyDebts>> GetByPeriodWithRaayahAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// التحقق من وجود ديون لرعوي في فترة معينة
        /// </summary>
        Task<bool> ExistsForPeriodAsync(int raayahId, DateTime fromDate, DateTime toDate);

        /// <summary>
        /// الحصول على آخر ديون لرعوي معين
        /// </summary>
        Task<WeeklyDebts?> GetLatestByRaayahAsync(int raayahId);

        /// <summary>
        /// حذف ديون فترة معينة
        /// </summary>
        Task DeleteByPeriodAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// الحصول على إحصائيات الديون لفترة معينة
        /// </summary>
        Task<(decimal TotalDebts, decimal TotalReceived, decimal TotalDiscount, decimal TotalNet)> GetPeriodStatisticsAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// الحصول على ديون الفروع لفترة معينة
        /// </summary>
        Task<(decimal Samir, decimal Maher, decimal Raid, decimal Haider, decimal Late)> GetBranchesTotalsAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// إدخال ديون أسبوعية جماعية
        /// </summary>
        Task<bool> BulkInsertWeeklyDebtsAsync(DateTime fromDate, DateTime toDate, Dictionary<int, WeeklyDebts> debtsData);
    }
}
