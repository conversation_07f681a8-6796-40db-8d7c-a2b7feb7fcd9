using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Helpers;
using GashmiDebtManagement.Services;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج إدخال البيانات الأسبوعية - تصميم عصري
    /// </summary>
    public partial class ModernWeeklyDataEntryForm : Form
    {
        private readonly IRaayahRepository _raayahRepository;
        private readonly IWeeklyDebtsRepository _weeklyDebtsRepository;
        
        // عناصر التحكم المحسنة
        private Panel? headerPanel;
        private Panel? periodPanel;
        private Panel? contentPanel;
        private Panel? summaryPanel;
        private Panel? buttonPanel;
        private Label? titleLabel;
        private Label? fromDateLabel;
        private DateTimePicker? fromDatePicker;
        private Label? toDateLabel;
        private DateTimePicker? toDatePicker;
        private Button? loadDataButton;
        private DataGridView? dataGridView;
        private Label? summaryLabel;
        private Button? saveButton;
        private Button? clearButton;
        private Button? cancelButton;

        // البيانات
        private List<Raayah> _raayahList = new();
        private Dictionary<int, WeeklyDebts> _weeklyDebtsData = new();

        public ModernWeeklyDataEntryForm(IRaayahRepository raayahRepository, IWeeklyDebtsRepository weeklyDebtsRepository)
        {
            _raayahRepository = raayahRepository;
            _weeklyDebtsRepository = weeklyDebtsRepository;
            InitializeComponent();
            InitializeModernDesign();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "إدخال البيانات الأسبوعية";
            this.Size = new Size(1400, 800);
            this.BackColor = UIHelper.ModernColors.Background;
            this.MinimumSize = new Size(1200, 600);

            // إنشاء التخطيط
            CreateModernLayout();

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void InitializeModernDesign()
        {
            // تطبيق دعم RTL المحسن
            RTLHelper.ApplyRTL(this);

            // إضافة اختصارات لوحة المفاتيح
            UIHelper.AddKeyboardShortcuts(this);
        }

        private void CreateModernLayout()
        {
            // ترتيب صحيح للطبقات - من الأسفل للأعلى

            // 1. إنشاء الأزرار أولاً (في الأسفل)
            CreateButtonPanel();

            // 2. إنشاء الملخص
            CreateSummaryPanel();

            // 3. إنشاء المحتوى (ليملأ المساحة المتبقية)
            CreateContentPanel();

            // 4. إنشاء لوحة الفترة
            CreatePeriodPanel();

            // 5. إنشاء الرأس أخيراً (في الأعلى)
            CreateHeaderPanel();

            // تحميل قائمة الرعية
            LoadRaayahListAsync();
        }

        private void CreateHeaderPanel()
        {
            headerPanel = new Panel
            {
                Height = 70,
                Dock = DockStyle.Top,
                BackColor = UIHelper.ModernColors.Primary,
                Padding = new Padding(UIHelper.Spacing.Large, UIHelper.Spacing.Medium, UIHelper.Spacing.Large, UIHelper.Spacing.Medium)
            };

            titleLabel = new Label
            {
                Text = "إدخال البيانات الأسبوعية",
                Font = UIHelper.ModernFonts.ArabicHeaderLarge,
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(UIHelper.Spacing.Large, 20)
            };

            headerPanel.Controls.Add(titleLabel);
            this.Controls.Add(headerPanel);
        }

        private void CreatePeriodPanel()
        {
            periodPanel = UIHelper.CreateCardPanel();
            periodPanel.Height = 80;
            periodPanel.Dock = DockStyle.Top;
            periodPanel.Margin = new Padding(UIHelper.Spacing.Medium, UIHelper.Spacing.Medium, UIHelper.Spacing.Medium, 0);

            // تسمية تاريخ البداية
            fromDateLabel = UIHelper.CreateArabicLabel("📅 من تاريخ:");
            fromDateLabel.Location = new Point(UIHelper.Spacing.Large, 25);

            // منتقي تاريخ البداية
            fromDatePicker = new DateTimePicker
            {
                Format = DateTimePickerFormat.Short,
                Font = UIHelper.ModernFonts.ArabicBody,
                Location = new Point(fromDateLabel.Right + UIHelper.Spacing.Small, 22),
                Size = new Size(150, UIHelper.Sizes.InputHeight),
                Value = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek)
            };
            fromDatePicker.ValueChanged += FromDatePicker_ValueChanged;

            // تسمية تاريخ النهاية
            toDateLabel = UIHelper.CreateArabicLabel("📅 إلى تاريخ:");
            toDateLabel.Location = new Point(fromDatePicker.Right + UIHelper.Spacing.Large, 25);

            // منتقي تاريخ النهاية
            toDatePicker = new DateTimePicker
            {
                Format = DateTimePickerFormat.Short,
                Font = UIHelper.ModernFonts.ArabicBody,
                Location = new Point(toDateLabel.Right + UIHelper.Spacing.Small, 22),
                Size = new Size(150, UIHelper.Sizes.InputHeight),
                Value = DateTime.Today.AddDays(6 - (int)DateTime.Today.DayOfWeek)
            };

            // زر تحميل البيانات
            loadDataButton = UIHelper.CreatePrimaryButton("📊 تحميل البيانات", LoadDataButton_Click);
            loadDataButton.Location = new Point(toDatePicker.Right + UIHelper.Spacing.Large, 20);
            loadDataButton.Size = new Size(150, UIHelper.Sizes.ButtonHeight);

            periodPanel.Controls.AddRange(new Control[] {
                fromDateLabel, fromDatePicker, toDateLabel, toDatePicker, loadDataButton
            });

            this.Controls.Add(periodPanel);
        }

        private void CreateContentPanel()
        {
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = UIHelper.ModernColors.Background,
                Padding = new Padding(UIHelper.Spacing.Medium, UIHelper.Spacing.Medium, UIHelper.Spacing.Medium, UIHelper.Spacing.Medium)
            };

            // إنشاء الجدول
            CreateDataGridView();

            this.Controls.Add(contentPanel);
        }

        private void CreateDataGridView()
        {
            dataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                RowHeadersVisible = false,
                SelectionMode = DataGridViewSelectionMode.CellSelect,
                EditMode = DataGridViewEditMode.EditOnEnter
            };

            // تطبيق التصميم العصري
            UIHelper.ApplyModernStyle(dataGridView);

            // إعداد الأعمدة
            SetupDataGridColumns();

            // الأحداث
            dataGridView.CellValueChanged += DataGridView_CellValueChanged;
            dataGridView.CellEndEdit += DataGridView_CellEndEdit;

            // تحميل البيانات الأولية سيتم لاحقاً
            // LoadDataAsync();

            contentPanel!.Controls.Add(dataGridView);
        }

        private void SetupDataGridColumns()
        {
            // عمود الاسم (للقراءة فقط)
            var nameColumn = new DataGridViewTextBoxColumn
            {
                Name = "FullName",
                HeaderText = "اسم الرعوي",
                DataPropertyName = "FullName",
                Width = 200,
                ReadOnly = true,
                DefaultCellStyle = { 
                    Alignment = DataGridViewContentAlignment.MiddleRight,
                    BackColor = UIHelper.ModernColors.Background
                }
            };

            // أعمدة الفروع
            var samirColumn = new DataGridViewTextBoxColumn
            {
                Name = "SamirAmount",
                HeaderText = "سمير",
                DataPropertyName = "SamirAmount",
                Width = 120,
                DefaultCellStyle = { 
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "N2"
                }
            };

            var maherColumn = new DataGridViewTextBoxColumn
            {
                Name = "MaherAmount",
                HeaderText = "ماهر",
                DataPropertyName = "MaherAmount",
                Width = 120,
                DefaultCellStyle = { 
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "N2"
                }
            };

            var raidColumn = new DataGridViewTextBoxColumn
            {
                Name = "RaidAmount",
                HeaderText = "رايد",
                DataPropertyName = "RaidAmount",
                Width = 120,
                DefaultCellStyle = { 
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "N2"
                }
            };

            var haiderColumn = new DataGridViewTextBoxColumn
            {
                Name = "HaiderAmount",
                HeaderText = "حيدر",
                DataPropertyName = "HaiderAmount",
                Width = 120,
                DefaultCellStyle = { 
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "N2"
                }
            };

            var lateColumn = new DataGridViewTextBoxColumn
            {
                Name = "LateAmount",
                HeaderText = "متأخر",
                DataPropertyName = "LateAmount",
                Width = 120,
                DefaultCellStyle = { 
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "N2"
                }
            };

            var receivedColumn = new DataGridViewTextBoxColumn
            {
                Name = "ReceivedAmount",
                HeaderText = "واصل",
                DataPropertyName = "ReceivedAmount",
                Width = 120,
                DefaultCellStyle = { 
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "N2"
                }
            };

            // عمود الإجمالي (للقراءة فقط)
            var totalColumn = new DataGridViewTextBoxColumn
            {
                Name = "TotalDebtsAmount",
                HeaderText = "إجمالي الديون",
                DataPropertyName = "TotalDebtsAmount",
                Width = 150,
                ReadOnly = true,
                DefaultCellStyle = { 
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "N2",
                    BackColor = UIHelper.ModernColors.Background,
                    ForeColor = UIHelper.ModernColors.Primary
                }
            };

            dataGridView!.Columns.AddRange(new DataGridViewColumn[] {
                nameColumn, samirColumn, maherColumn, raidColumn, haiderColumn, 
                lateColumn, receivedColumn, totalColumn
            });
        }

        private void CreateSummaryPanel()
        {
            summaryPanel = UIHelper.CreateCardPanel();
            summaryPanel.Height = 60;
            summaryPanel.Dock = DockStyle.Bottom;
            summaryPanel.Margin = new Padding(UIHelper.Spacing.Medium, 0, UIHelper.Spacing.Medium, UIHelper.Spacing.Medium);

            summaryLabel = new Label
            {
                Text = "الملخص: 0 رعوي | إجمالي الديون: 0.00 ريال | إجمالي الواصل: 0.00 ريال",
                Font = UIHelper.ModernFonts.HeaderSmall,
                ForeColor = UIHelper.ModernColors.Primary,
                AutoSize = true,
                Location = new Point(UIHelper.Spacing.Large, 20)
            };

            summaryPanel.Controls.Add(summaryLabel);
            this.Controls.Add(summaryPanel);
        }

        private void CreateButtonPanel()
        {
            buttonPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Bottom,
                BackColor = UIHelper.ModernColors.Surface,
                Padding = new Padding(UIHelper.Spacing.Large, UIHelper.Spacing.Medium, UIHelper.Spacing.Large, UIHelper.Spacing.Medium)
            };

            // زر الحفظ
            saveButton = UIHelper.CreateSuccessButton("💾 حفظ البيانات", SaveButton_Click);
            saveButton.Size = new Size(150, UIHelper.Sizes.ButtonHeight);
            saveButton.Location = new Point(
                buttonPanel.Width - saveButton.Width - UIHelper.Spacing.Large - 320,
                (buttonPanel.Height - saveButton.Height) / 2
            );
            saveButton.Enabled = false;

            // زر المسح
            clearButton = UIHelper.CreateSecondaryButton("🗑️ مسح البيانات", ClearButton_Click);
            clearButton.Size = new Size(150, UIHelper.Sizes.ButtonHeight);
            clearButton.Location = new Point(
                buttonPanel.Width - clearButton.Width - UIHelper.Spacing.Large - 160,
                (buttonPanel.Height - clearButton.Height) / 2
            );
            clearButton.Enabled = false;

            // زر الإلغاء
            cancelButton = UIHelper.CreateSecondaryButton("❌ إغلاق", CancelButton_Click);
            cancelButton.Size = new Size(150, UIHelper.Sizes.ButtonHeight);
            cancelButton.Location = new Point(
                buttonPanel.Width - cancelButton.Width - UIHelper.Spacing.Large,
                (buttonPanel.Height - cancelButton.Height) / 2
            );

            buttonPanel.Controls.AddRange(new Control[] { saveButton, clearButton, cancelButton });
            this.Controls.Add(buttonPanel);
        }

        #region معالجات الأحداث

        private async void LoadRaayahListAsync()
        {
            try
            {
                _raayahList = (await _raayahRepository.GetAllAsync()).OrderBy(r => r.FullName).ToList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الرعية:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FromDatePicker_ValueChanged(object? sender, EventArgs e)
        {
            // تحديث تاريخ النهاية تلقائياً (7 أيام)
            toDatePicker!.Value = fromDatePicker!.Value.AddDays(6);
        }

        private async void LoadDataButton_Click(object? sender, EventArgs e)
        {
            try
            {
                loadDataButton!.Enabled = false;
                loadDataButton.Text = "جاري التحميل...";

                var fromDate = fromDatePicker!.Value.Date;
                var toDate = toDatePicker!.Value.Date;

                // التحقق من صحة الفترة
                if (toDate < fromDate)
                {
                    MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية",
                        "خطأ في التاريخ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // التحقق من وجود بيانات لهذه الفترة
                var existingData = await _weeklyDebtsRepository.GetByPeriodAsync(fromDate, toDate);

                if (existingData.Any())
                {
                    var result = MessageBox.Show(
                        "يوجد بيانات مسجلة لهذه الفترة! هل تريد تحميلها للتعديل؟",
                        "بيانات موجودة",
                        MessageBoxButtons.YesNoCancel,
                        MessageBoxIcon.Question
                    );

                    if (result == DialogResult.Cancel)
                        return;
                    else if (result == DialogResult.Yes)
                    {
                        LoadExistingData(existingData);
                        return;
                    }
                }

                // إنشاء بيانات جديدة
                CreateNewDataRows(fromDate, toDate);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                loadDataButton!.Enabled = true;
                loadDataButton.Text = "📊 تحميل البيانات";
            }
        }

        private void LoadExistingData(IEnumerable<WeeklyDebts> existingData)
        {
            _weeklyDebtsData.Clear();

            foreach (var debt in existingData)
            {
                _weeklyDebtsData[debt.RaayahId] = debt;
            }

            // إنشاء صفوف للرعية الذين ليس لديهم بيانات
            var fromDate = fromDatePicker!.Value.Date;
            var toDate = toDatePicker!.Value.Date;

            foreach (var raayah in _raayahList)
            {
                if (!_weeklyDebtsData.ContainsKey(raayah.Id))
                {
                    _weeklyDebtsData[raayah.Id] = new WeeklyDebts
                    {
                        RaayahId = raayah.Id,
                        Raayah = raayah,
                        DateFrom = fromDate,
                        DateTo = toDate
                    };
                }
            }

            UpdateDataGridView();
            EnableButtons();
        }

        private void CreateNewDataRows(DateTime fromDate, DateTime toDate)
        {
            _weeklyDebtsData.Clear();

            foreach (var raayah in _raayahList)
            {
                _weeklyDebtsData[raayah.Id] = new WeeklyDebts
                {
                    RaayahId = raayah.Id,
                    Raayah = raayah,
                    DateFrom = fromDate,
                    DateTo = toDate
                };
            }

            UpdateDataGridView();
            EnableButtons();
        }

        private void UpdateDataGridView()
        {
            var dataList = _weeklyDebtsData.Values.OrderBy(w => w.Raayah?.FullName).ToList();
            dataGridView!.DataSource = dataList;

            UpdateSummary();
        }

        private void EnableButtons()
        {
            saveButton!.Enabled = true;
            clearButton!.Enabled = true;
        }

        private void DataGridView_CellValueChanged(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                CalculateRowTotal(e.RowIndex);
                UpdateSummary();
            }
        }

        private void DataGridView_CellEndEdit(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                CalculateRowTotal(e.RowIndex);
                UpdateSummary();
            }
        }

        private void CalculateRowTotal(int rowIndex)
        {
            var row = dataGridView!.Rows[rowIndex];
            var weeklyDebt = row.DataBoundItem as WeeklyDebts;

            if (weeklyDebt != null)
            {
                // تحديث الإجمالي
                row.Cells["TotalDebtsAmount"].Value = weeklyDebt.TotalDebtsAmount;
            }
        }

        private void UpdateSummary()
        {
            var totalRaayah = _weeklyDebtsData.Count;
            var totalDebts = _weeklyDebtsData.Values.Sum(w => w.TotalDebtsAmount);
            var totalReceived = _weeklyDebtsData.Values.Sum(w => w.ReceivedAmount);

            summaryLabel!.Text = $"الملخص: {totalRaayah} رعوي | " +
                                $"إجمالي الديون: {totalDebts:N2} ريال | " +
                                $"إجمالي الواصل: {totalReceived:N2} ريال";
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                saveButton!.Enabled = false;
                saveButton.Text = "جاري الحفظ...";

                var fromDate = fromDatePicker!.Value.Date;
                var toDate = toDatePicker!.Value.Date;

                var result = await _weeklyDebtsRepository.BulkInsertWeeklyDebtsAsync(
                    fromDate, toDate, _weeklyDebtsData);

                if (result)
                {
                    MessageBox.Show("تم حفظ البيانات بنجاح", "نجح الحفظ",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("فشل في حفظ البيانات", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                saveButton!.Enabled = true;
                saveButton.Text = "💾 حفظ البيانات";
            }
        }

        private void ClearButton_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من مسح جميع البيانات؟",
                "تأكيد المسح",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question
            );

            if (result == DialogResult.Yes)
            {
                foreach (var debt in _weeklyDebtsData.Values)
                {
                    debt.SamirAmount = 0;
                    debt.MaherAmount = 0;
                    debt.RaidAmount = 0;
                    debt.HaiderAmount = 0;
                    debt.LateAmount = 0;
                    debt.ReceivedAmount = 0;
                }

                UpdateDataGridView();
            }
        }

        private void CancelButton_Click(object? sender, EventArgs e)
        {
            this.Close();
        }

        #endregion
    }
}
