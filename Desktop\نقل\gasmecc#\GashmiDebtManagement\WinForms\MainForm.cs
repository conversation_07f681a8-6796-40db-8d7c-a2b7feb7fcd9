using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Services;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// النافذة الرئيسية للتطبيق
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly ApplicationDbContext _context;
        private readonly IRaayahRepository _raayahRepository;
        private readonly IWeeklyDebtsRepository _weeklyDebtsRepository;

        public MainForm()
        {
            InitializeComponent();
            InitializeArabicSupport();

            _context = new ApplicationDbContext();
            _raayahRepository = new RaayahRepository(_context);
            _weeklyDebtsRepository = new WeeklyDebtsRepository(_context);

            // التأكد من إنشاء قاعدة البيانات
            _context.EnsureCreated();

            // نسخة احتياطية تلقائية
            BackupService.AutoBackupOnStartup();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النافذة الرئيسية
            this.Text = "نظام إدارة ديون الرعية - شركة الغشمي";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.Icon = SystemIcons.Application;

            // إنشاء شريط القوائم
            CreateMenuStrip();

            // إنشاء شريط الأدوات
            CreateToolStrip();

            // إنشاء شريط الحالة
            CreateStatusStrip();

            // إنشاء اللوحة الرئيسية
            CreateMainPanel();

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void InitializeArabicSupport()
        {
            // دعم اللغة العربية
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // تعيين الخط العربي
            this.Font = new Font("Tahoma", 10F, FontStyle.Regular);
        }

        private MenuStrip? menuStrip;
        private ToolStrip? toolStrip;
        private StatusStrip? statusStrip;
        private Panel? mainPanel;
        private Label? welcomeLabel;

        private void CreateMenuStrip()
        {
            menuStrip = new MenuStrip();
            menuStrip.RightToLeft = RightToLeft.Yes;
            menuStrip.Font = new Font("Tahoma", 10F);

            // قائمة الرعية
            var raayahMenu = new ToolStripMenuItem("إدارة الرعية");
            raayahMenu.DropDownItems.Add("عرض قائمة الرعية", null, (s, e) => ShowRaayahList());
            raayahMenu.DropDownItems.Add("إضافة رعوي جديد", null, (s, e) => ShowAddRaayah());
            raayahMenu.DropDownItems.Add(new ToolStripSeparator());
            raayahMenu.DropDownItems.Add("البحث عن رعوي", null, (s, e) => ShowSearchRaayah());

            // قائمة البيانات الأسبوعية
            var weeklyMenu = new ToolStripMenuItem("البيانات الأسبوعية");
            weeklyMenu.DropDownItems.Add("إدخال بيانات جديدة", null, (s, e) => ShowWeeklyDataEntry());
            weeklyMenu.DropDownItems.Add("عرض البيانات الأسبوعية", null, (s, e) => ShowWeeklyDataView());
            weeklyMenu.DropDownItems.Add("تعديل البيانات", null, (s, e) => ShowWeeklyDataEdit());

            // قائمة التقارير
            var reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.DropDownItems.Add("التقرير الكامل", null, (s, e) => ShowCompleteReport());
            reportsMenu.DropDownItems.Add("التقرير مع خصم الحوالة", null, (s, e) => ShowDiscountReport());
            reportsMenu.DropDownItems.Add("كشف البطاقات", null, (s, e) => ShowCardsReport());
            reportsMenu.DropDownItems.Add("كشف الأوزري", null, (s, e) => ShowOzriReport());
            reportsMenu.DropDownItems.Add("خارج الكشف", null, (s, e) => ShowKharijReport());
            reportsMenu.DropDownItems.Add(new ToolStripSeparator());
            reportsMenu.DropDownItems.Add("تقرير الفروع", null, (s, e) => ShowBranchesReport());
            reportsMenu.DropDownItems.Add("التقرير الشهري", null, (s, e) => ShowMonthlyReport());

            // قائمة الأدوات
            var toolsMenu = new ToolStripMenuItem("الأدوات");
            toolsMenu.DropDownItems.Add("النسخ الاحتياطية", null, (s, e) => ShowBackupManager());
            toolsMenu.DropDownItems.Add("تصدير البيانات", null, (s, e) => ShowExportManager());
            toolsMenu.DropDownItems.Add("الإحصائيات", null, (s, e) => ShowStatistics());
            toolsMenu.DropDownItems.Add(new ToolStripSeparator());
            toolsMenu.DropDownItems.Add("اختبار النظام", null, (s, e) => ShowSystemTests());

            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add("دليل المستخدم", null, (s, e) => ShowUserGuide());
            helpMenu.DropDownItems.Add("حول البرنامج", null, (s, e) => ShowAbout());

            menuStrip.Items.AddRange(new ToolStripItem[] {
                raayahMenu, weeklyMenu, reportsMenu, toolsMenu, helpMenu
            });

            this.Controls.Add(menuStrip);
            this.MainMenuStrip = menuStrip;
        }

        private void CreateToolStrip()
        {
            toolStrip = new ToolStrip();
            toolStrip.RightToLeft = RightToLeft.Yes;
            toolStrip.Font = new Font("Tahoma", 9F);

            // أزرار الأدوات الرئيسية
            var addRaayahBtn = new ToolStripButton("إضافة رعوي", null, (s, e) => ShowAddRaayah());
            var weeklyDataBtn = new ToolStripButton("إدخال بيانات أسبوعية", null, (s, e) => ShowWeeklyDataEntry());
            var reportsBtn = new ToolStripButton("التقارير", null, (s, e) => ShowReportsMenu());
            var backupBtn = new ToolStripButton("نسخة احتياطية", null, (s, e) => CreateBackup());

            toolStrip.Items.AddRange(new ToolStripItem[] {
                addRaayahBtn,
                new ToolStripSeparator(),
                weeklyDataBtn,
                new ToolStripSeparator(),
                reportsBtn,
                new ToolStripSeparator(),
                backupBtn
            });

            this.Controls.Add(toolStrip);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip();
            statusStrip.RightToLeft = RightToLeft.Yes;
            statusStrip.Font = new Font("Tahoma", 9F);

            var statusLabel = new ToolStripStatusLabel("جاهز");
            var dateLabel = new ToolStripStatusLabel($"التاريخ: {DateTime.Now:dd/MM/yyyy}");
            var timeLabel = new ToolStripStatusLabel($"الوقت: {DateTime.Now:HH:mm}");

            statusStrip.Items.AddRange(new ToolStripItem[] {
                statusLabel,
                new ToolStripStatusLabel() { Spring = true },
                dateLabel,
                timeLabel
            });

            this.Controls.Add(statusStrip);
        }

        private void CreateMainPanel()
        {
            mainPanel = new Panel();
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.BackColor = Color.WhiteSmoke;

            // رسالة الترحيب
            welcomeLabel = new Label();
            welcomeLabel.Text = "مرحباً بك في نظام إدارة ديون الرعية\nشركة الغشمي\n\nاختر من القائمة أعلاه للبدء";
            welcomeLabel.Font = new Font("Tahoma", 16F, FontStyle.Bold);
            welcomeLabel.ForeColor = Color.DarkBlue;
            welcomeLabel.TextAlign = ContentAlignment.MiddleCenter;
            welcomeLabel.Dock = DockStyle.Fill;

            mainPanel.Controls.Add(welcomeLabel);
            this.Controls.Add(mainPanel);
        }

        // معالجات الأحداث للقوائم
        private void ShowRaayahList()
        {
            var form = new RaayahListForm(_raayahRepository);
            ShowFormInPanel(form);
        }

        private void ShowAddRaayah()
        {
            var form = new AddRaayahForm(_raayahRepository);
            form.RaayahAdded += (s, e) => RefreshCurrentView();
            ShowFormInPanel(form);
        }

        private void ShowSearchRaayah()
        {
            var form = new SearchRaayahForm(_raayahRepository);
            ShowFormInPanel(form);
        }

        private void ShowWeeklyDataEntry()
        {
            var form = new WeeklyDataEntryForm(_raayahRepository, _weeklyDebtsRepository);
            ShowFormInPanel(form);
        }

        private void ShowWeeklyDataView()
        {
            var form = new WeeklyDataViewForm(_weeklyDebtsRepository);
            ShowFormInPanel(form);
        }

        private void ShowWeeklyDataEdit()
        {
            var form = new WeeklyDataEditForm(_weeklyDebtsRepository);
            ShowFormInPanel(form);
        }

        private void ShowCompleteReport()
        {
            var form = new CompleteReportForm(_weeklyDebtsRepository);
            ShowFormInPanel(form);
        }

        private void ShowDiscountReport()
        {
            var form = new DiscountReportForm(_weeklyDebtsRepository);
            ShowFormInPanel(form);
        }

        private void ShowCardsReport()
        {
            var form = new CardsReportForm(_weeklyDebtsRepository);
            ShowFormInPanel(form);
        }

        private void ShowOzriReport()
        {
            var form = new OzriReportForm(_raayahRepository, _weeklyDebtsRepository);
            ShowFormInPanel(form);
        }

        private void ShowKharijReport()
        {
            var form = new KharijReportForm(_raayahRepository, _weeklyDebtsRepository);
            ShowFormInPanel(form);
        }

        private void ShowBranchesReport()
        {
            var form = new BranchesReportForm(_weeklyDebtsRepository);
            ShowFormInPanel(form);
        }

        private void ShowMonthlyReport()
        {
            var form = new MonthlyReportForm(_weeklyDebtsRepository);
            ShowFormInPanel(form);
        }

        private void ShowBackupManager()
        {
            var form = new BackupManagerForm();
            ShowFormInPanel(form);
        }

        private void ShowExportManager()
        {
            var form = new ExportManagerForm(_weeklyDebtsRepository, _raayahRepository);
            ShowFormInPanel(form);
        }

        private void ShowStatistics()
        {
            var form = new StatisticsForm(_raayahRepository, _weeklyDebtsRepository);
            ShowFormInPanel(form);
        }

        private void ShowSystemTests()
        {
            var form = new SystemTestsForm();
            ShowFormInPanel(form);
        }

        private void ShowReportsMenu()
        {
            // عرض قائمة منبثقة للتقارير
            var contextMenu = new ContextMenuStrip();
            contextMenu.Items.Add("التقرير الكامل", null, (s, e) => ShowCompleteReport());
            contextMenu.Items.Add("التقرير مع خصم الحوالة", null, (s, e) => ShowDiscountReport());
            contextMenu.Items.Add("كشف البطاقات", null, (s, e) => ShowCardsReport());
            contextMenu.Show(Cursor.Position);
        }

        private void CreateBackup()
        {
            try
            {
                var backupPath = BackupService.CreateBackup();
                MessageBox.Show($"تم إنشاء النسخة الاحتياطية بنجاح:\n{backupPath}",
                    "نسخة احتياطية", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في إنشاء النسخة الاحتياطية:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowUserGuide()
        {
            try
            {
                var guidePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "USER_GUIDE.md");
                if (File.Exists(guidePath))
                {
                    System.Diagnostics.Process.Start("notepad.exe", guidePath);
                }
                else
                {
                    MessageBox.Show("ملف دليل المستخدم غير موجود", "تحذير",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في فتح دليل المستخدم:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowAbout()
        {
            var aboutText = @"نظام إدارة ديون الرعية
شركة الغشمي

الإصدار: 1.0.0
تاريخ التطوير: يوليو 2024
المطور: Augment Agent

جميع الحقوق محفوظة © 2024";

            MessageBox.Show(aboutText, "حول البرنامج",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowFormInPanel(Form form)
        {
            if (mainPanel == null) return;

            // إزالة المحتوى الحالي
            mainPanel.Controls.Clear();

            // إعداد النموذج ليعمل كـ UserControl
            form.TopLevel = false;
            form.FormBorderStyle = FormBorderStyle.None;
            form.Dock = DockStyle.Fill;

            // إضافة النموذج للوحة الرئيسية
            mainPanel.Controls.Add(form);
            form.Show();
        }

        private void RefreshCurrentView()
        {
            // تحديث العرض الحالي إذا لزم الأمر
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من إغلاق البرنامج؟",
                "تأكيد الإغلاق", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.No)
            {
                e.Cancel = true;
                return;
            }

            // تنظيف الموارد
            _context?.Dispose();
            base.OnFormClosing(e);
        }
    }
}
