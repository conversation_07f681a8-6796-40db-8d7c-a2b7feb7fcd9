using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Helpers;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج قائمة الرعية - تصميم عصري
    /// </summary>
    public partial class ModernRaayahListForm : Form
    {
        private readonly IRaayahRepository _raayahRepository;
        
        // عناصر التحكم المحسنة
        private Panel? headerPanel;
        private Panel? toolbarPanel;
        private Panel? contentPanel;
        private Panel? statusPanel;
        private Label? titleLabel;
        private TextBox? searchTextBox;
        private Button? addButton;
        private Button? editButton;
        private Button? deleteButton;
        private Button? refreshButton;
        private DataGridView? dataGridView;
        private Label? statusLabel;
        private Label? countLabel;

        // البيانات
        private List<Raayah> _allRaayah = new();
        private List<Raayah> _filteredRaayah = new();

        public ModernRaayahListForm(IRaayahRepository raayahRepository)
        {
            _raayahRepository = raayahRepository;
            InitializeComponent();
            InitializeModernDesign();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "قائمة الرعية";
            this.Size = new Size(1200, 700);
            this.BackColor = UIHelper.ModernColors.Background;
            this.MinimumSize = new Size(800, 500);

            // إنشاء التخطيط
            CreateModernLayout();

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void InitializeModernDesign()
        {
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = UIHelper.ModernFonts.ArabicBody;

            // إضافة اختصارات لوحة المفاتيح
            UIHelper.AddKeyboardShortcuts(this);
        }

        private void CreateModernLayout()
        {
            // إنشاء الرأس
            CreateHeaderPanel();

            // إنشاء شريط الأدوات
            CreateToolbarPanel();

            // إنشاء المحتوى
            CreateContentPanel();

            // إنشاء شريط الحالة
            CreateStatusPanel();

            // تحميل البيانات
            LoadDataAsync();
        }

        private void CreateHeaderPanel()
        {
            headerPanel = new Panel
            {
                Height = 70,
                Dock = DockStyle.Top,
                BackColor = UIHelper.ModernColors.Primary,
                Padding = new Padding(UIHelper.Spacing.Large, UIHelper.Spacing.Medium, UIHelper.Spacing.Large, UIHelper.Spacing.Medium)
            };

            titleLabel = new Label
            {
                Text = "قائمة الرعية",
                Font = UIHelper.ModernFonts.ArabicHeaderLarge,
                ForeColor = Color.White,
                AutoSize = true,
                Location = new Point(UIHelper.Spacing.Large, 20)
            };

            headerPanel.Controls.Add(titleLabel);
            this.Controls.Add(headerPanel);
        }

        private void CreateToolbarPanel()
        {
            toolbarPanel = UIHelper.CreateToolbarPanel();

            // مربع البحث
            var searchLabel = new Label
            {
                Text = "🔍 البحث:",
                Font = UIHelper.ModernFonts.ArabicBody,
                ForeColor = UIHelper.ModernColors.TextPrimary,
                AutoSize = true,
                Location = new Point(UIHelper.Spacing.Large, 15)
            };

            searchTextBox = UIHelper.CreateModernTextBox("ابحث عن رعوي...");
            searchTextBox.Location = new Point(searchLabel.Right + UIHelper.Spacing.Small, 12);
            searchTextBox.Size = new Size(250, UIHelper.Sizes.InputHeight);
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            // الأزرار
            var buttonY = 8;
            var buttonSpacing = UIHelper.Spacing.Small;

            addButton = UIHelper.CreatePrimaryButton("➕ إضافة رعوي", AddButton_Click);
            addButton.Location = new Point(searchTextBox.Right + UIHelper.Spacing.Large, buttonY);
            addButton.Size = new Size(120, UIHelper.Sizes.ButtonHeight);

            editButton = UIHelper.CreateSecondaryButton("✏️ تعديل", EditButton_Click);
            editButton.Location = new Point(addButton.Right + buttonSpacing, buttonY);
            editButton.Size = new Size(100, UIHelper.Sizes.ButtonHeight);
            editButton.Enabled = false;

            deleteButton = UIHelper.CreateDangerButton("🗑️ حذف", DeleteButton_Click);
            deleteButton.Location = new Point(editButton.Right + buttonSpacing, buttonY);
            deleteButton.Size = new Size(100, UIHelper.Sizes.ButtonHeight);
            deleteButton.Enabled = false;

            refreshButton = UIHelper.CreateSecondaryButton("🔄 تحديث", RefreshButton_Click);
            refreshButton.Location = new Point(deleteButton.Right + buttonSpacing, buttonY);
            refreshButton.Size = new Size(100, UIHelper.Sizes.ButtonHeight);

            toolbarPanel.Controls.AddRange(new Control[] {
                searchLabel, searchTextBox, addButton, editButton, deleteButton, refreshButton
            });

            this.Controls.Add(toolbarPanel);
        }

        private void CreateContentPanel()
        {
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = UIHelper.ModernColors.Background,
                Padding = new Padding(UIHelper.Spacing.Medium, UIHelper.Spacing.Medium, UIHelper.Spacing.Medium, UIHelper.Spacing.Medium)
            };

            // إنشاء الجدول
            CreateDataGridView();

            this.Controls.Add(contentPanel);
        }

        private void CreateDataGridView()
        {
            dataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                RowHeadersVisible = false
            };

            // تطبيق التصميم العصري
            UIHelper.ApplyModernStyle(dataGridView);

            // إعداد الأعمدة
            SetupColumns();

            // الأحداث
            dataGridView.SelectionChanged += DataGridView_SelectionChanged;
            dataGridView.CellDoubleClick += DataGridView_CellDoubleClick;

            contentPanel!.Controls.Add(dataGridView);
        }

        private void SetupColumns()
        {
            // عمود الرقم
            var idColumn = new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "الرقم",
                DataPropertyName = "Id",
                Width = 80,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            };

            // عمود الاسم
            var nameColumn = new DataGridViewTextBoxColumn
            {
                Name = "FullName",
                HeaderText = "الاسم الكامل",
                DataPropertyName = "FullName",
                Width = 300,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleRight }
            };

            // عمود خصم الحوالة
            var discountColumn = new DataGridViewCheckBoxColumn
            {
                Name = "EnableDiscount",
                HeaderText = "خصم الحوالة",
                DataPropertyName = "EnableDiscount",
                Width = 120,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            };

            // عمود كشف الأوزري
            var ozriColumn = new DataGridViewCheckBoxColumn
            {
                Name = "InKashfOzri",
                HeaderText = "كشف الأوزري",
                DataPropertyName = "InKashfOzri",
                Width = 120,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            };

            // عمود خارج الكشف
            var kharijColumn = new DataGridViewCheckBoxColumn
            {
                Name = "InKharijKashf",
                HeaderText = "خارج الكشف",
                DataPropertyName = "InKharijKashf",
                Width = 120,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            };

            // عمود تاريخ الإنشاء
            var dateColumn = new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedDate",
                Width = 150,
                DefaultCellStyle = { 
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "dd/MM/yyyy"
                }
            };

            dataGridView!.Columns.AddRange(new DataGridViewColumn[] {
                idColumn, nameColumn, discountColumn, ozriColumn, kharijColumn, dateColumn
            });
        }

        private void CreateStatusPanel()
        {
            statusPanel = new Panel
            {
                Height = UIHelper.Sizes.StatusBarHeight + UIHelper.Spacing.Small,
                Dock = DockStyle.Bottom,
                BackColor = UIHelper.ModernColors.Surface,
                Padding = new Padding(UIHelper.Spacing.Medium, UIHelper.Spacing.Small, UIHelper.Spacing.Medium, UIHelper.Spacing.Small)
            };

            statusLabel = new Label
            {
                Text = "جاري التحميل...",
                Font = UIHelper.ModernFonts.Caption,
                ForeColor = UIHelper.ModernColors.TextSecondary,
                AutoSize = true,
                Location = new Point(UIHelper.Spacing.Medium, UIHelper.Spacing.Small)
            };

            countLabel = new Label
            {
                Text = "0 رعوي",
                Font = UIHelper.ModernFonts.Caption,
                ForeColor = UIHelper.ModernColors.TextMuted,
                AutoSize = true,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left
            };

            statusPanel.Controls.AddRange(new Control[] { statusLabel, countLabel });
            this.Controls.Add(statusPanel);
        }

        #region معالجات الأحداث

        private async void LoadDataAsync()
        {
            try
            {
                UpdateStatus("جاري تحميل البيانات...");

                _allRaayah = (await _raayahRepository.GetAllAsync()).ToList();
                _filteredRaayah = new List<Raayah>(_allRaayah);

                UpdateDataGridView();
                UpdateStatus("تم تحميل البيانات بنجاح");
            }
            catch (Exception ex)
            {
                UpdateStatus($"خطأ في تحميل البيانات: {ex.Message}");
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateDataGridView()
        {
            dataGridView!.DataSource = null;
            dataGridView.DataSource = _filteredRaayah;

            UpdateCountLabel();
            UpdateButtonStates();
        }

        private void UpdateCountLabel()
        {
            var total = _allRaayah.Count;
            var filtered = _filteredRaayah.Count;

            if (total == filtered)
            {
                countLabel!.Text = $"{total} رعوي";
            }
            else
            {
                countLabel!.Text = $"{filtered} من {total} رعوي";
            }

            // تحديث موقع العداد
            countLabel.Location = new Point(
                statusPanel!.Width - countLabel.Width - UIHelper.Spacing.Medium,
                UIHelper.Spacing.Small
            );
        }

        private void UpdateButtonStates()
        {
            var hasSelection = dataGridView!.SelectedRows.Count > 0;
            editButton!.Enabled = hasSelection;
            deleteButton!.Enabled = hasSelection;
        }

        private void UpdateStatus(string message)
        {
            if (statusLabel != null)
            {
                statusLabel.Text = message;
                statusLabel.Refresh();
            }
        }

        private void SearchTextBox_TextChanged(object? sender, EventArgs e)
        {
            var searchTerm = searchTextBox?.Text?.Trim() ?? "";

            if (string.IsNullOrEmpty(searchTerm))
            {
                _filteredRaayah = new List<Raayah>(_allRaayah);
            }
            else
            {
                _filteredRaayah = _allRaayah
                    .Where(r => r.FullName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                    .ToList();
            }

            UpdateDataGridView();
        }

        private void DataGridView_SelectionChanged(object? sender, EventArgs e)
        {
            UpdateButtonStates();
        }

        private void DataGridView_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditSelectedRaayah();
            }
        }

        private void AddButton_Click(object? sender, EventArgs e)
        {
            var addForm = new ModernAddRaayahForm(_raayahRepository);
            addForm.RaayahAdded += (s, args) => LoadDataAsync();

            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadDataAsync();
            }
        }

        private void EditButton_Click(object? sender, EventArgs e)
        {
            EditSelectedRaayah();
        }

        private void EditSelectedRaayah()
        {
            var selectedRaayah = GetSelectedRaayah();
            if (selectedRaayah != null)
            {
                var editForm = new EditRaayahForm(_raayahRepository, selectedRaayah);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadDataAsync();
                }
            }
        }

        private async void DeleteButton_Click(object? sender, EventArgs e)
        {
            var selectedRaayah = GetSelectedRaayah();
            if (selectedRaayah == null) return;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف الرعوي '{selectedRaayah.FullName}'؟\n\nسيتم حذف جميع البيانات المرتبطة به.",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning,
                MessageBoxDefaultButton.Button2
            );

            if (result == DialogResult.Yes)
            {
                try
                {
                    UpdateStatus("جاري حذف الرعوي...");
                    await _raayahRepository.DeleteAsync(selectedRaayah);
                    LoadDataAsync();
                    UpdateStatus("تم حذف الرعوي بنجاح");
                }
                catch (Exception ex)
                {
                    UpdateStatus($"خطأ في حذف الرعوي: {ex.Message}");
                    MessageBox.Show($"حدث خطأ أثناء حذف الرعوي:\n{ex.Message}",
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void RefreshButton_Click(object? sender, EventArgs e)
        {
            LoadDataAsync();
        }

        private Raayah? GetSelectedRaayah()
        {
            if (dataGridView?.SelectedRows.Count > 0)
            {
                return dataGridView.SelectedRows[0].DataBoundItem as Raayah;
            }
            return null;
        }

        #endregion
    }
}
