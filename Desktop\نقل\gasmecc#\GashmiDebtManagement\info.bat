@echo off
chcp 65001 > nul
title معلومات نظام إدارة ديون الرعية

echo ========================================
echo    معلومات نظام إدارة ديون الرعية
echo    شركة الغشمي
echo ========================================
echo.

echo 📋 معلومات المشروع:
echo ├─ الاسم: نظام إدارة ديون الرعية
echo ├─ الشركة: شركة الغشمي
echo ├─ الإصدار: 1.0.0
echo ├─ تاريخ التطوير: يوليو 2024
echo └─ المطور: Augment Agent
echo.

echo 🖥️ معلومات النظام:
echo ├─ نظام التشغيل: %OS%
echo ├─ اسم الجهاز: %COMPUTERNAME%
echo ├─ اسم المستخدم: %USERNAME%
echo └─ التاريخ والوقت: %DATE% %TIME%
echo.

echo 🔧 معلومات .NET:
dotnet --info 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ .NET غير مثبت أو غير متاح
) else (
    echo ✅ .NET متوفر
)
echo.

echo 📁 معلومات المجلدات:
if exist "Data" (
    echo ✅ Data - قاعدة البيانات
) else (
    echo ❌ Data - غير موجود
)

if exist "Backups" (
    echo ✅ Backups - النسخ الاحتياطية
) else (
    echo ❌ Backups - غير موجود
)

if exist "Exports" (
    echo ✅ Exports - الملفات المصدرة
) else (
    echo ❌ Exports - غير موجود
)

if exist "Logs" (
    echo ✅ Logs - ملفات السجلات
) else (
    echo ❌ Logs - غير موجود
)
echo.

echo 📊 إحصائيات الملفات:
if exist "Data\*.db" (
    for %%f in (Data\*.db) do (
        echo ├─ قاعدة البيانات: %%~nxf ^(%%~zf bytes^)
    )
) else (
    echo ├─ قاعدة البيانات: غير موجودة
)

if exist "Backups\*.zip" (
    set /a backupCount=0
    for %%f in (Backups\*.zip) do set /a backupCount+=1
    echo ├─ النسخ الاحتياطية: !backupCount! ملف
) else (
    echo ├─ النسخ الاحتياطية: لا توجد ملفات
)

if exist "Exports\*.csv" (
    set /a exportCount=0
    for %%f in (Exports\*.csv) do set /a exportCount+=1
    echo └─ الملفات المصدرة: !exportCount! ملف
) else (
    echo └─ الملفات المصدرة: لا توجد ملفات
)
echo.

echo 🚀 الأوامر المتاحة:
echo ├─ setup.bat    - إعداد النظام
echo ├─ run.bat      - تشغيل التطبيق
echo ├─ test.bat     - تشغيل الاختبارات
echo ├─ build.bat    - بناء المشروع
echo ├─ clean.bat    - تنظيف المشروع
echo └─ info.bat     - عرض هذه المعلومات
echo.

echo 📞 الدعم الفني:
echo ├─ دليل المستخدم: USER_GUIDE.md
echo ├─ ملف README: README.md
echo └─ قائمة المساعدة في التطبيق
echo.

echo 🔗 روابط مفيدة:
echo ├─ تحميل .NET: https://dotnet.microsoft.com/download
echo ├─ دليل C#: https://docs.microsoft.com/dotnet/csharp/
echo └─ Entity Framework: https://docs.microsoft.com/ef/
echo.

pause
