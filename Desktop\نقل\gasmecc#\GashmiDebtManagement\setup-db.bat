@echo off
chcp 65001 > nul
title إعداد قاعدة البيانات - نظام إدارة ديون الرعية

echo ========================================
echo    إعداد قاعدة البيانات
echo    نظام إدارة ديون الرعية
echo ========================================
echo.

echo جاري التحقق من قاعدة البيانات...
echo.

REM التحقق من وجود مجلد البيانات
if not exist "Data" (
    mkdir Data
    echo ✅ تم إنشاء مجلد Data
)

REM التحقق من وجود قاعدة البيانات
if exist "Data\GashmiDebtManagement.db" (
    echo ℹ️ قاعدة البيانات موجودة مسبقاً
    echo.
    echo هل تريد إعادة إنشاء قاعدة البيانات؟
    echo تحذير: سيتم حذف جميع البيانات الموجودة!
    echo.
    set /p recreate=اكتب 'نعم' لإعادة الإنشاء أو اضغط Enter للمتابعة: 
    
    if /i "%recreate%" EQU "نعم" (
        echo.
        echo جاري حذف قاعدة البيانات القديمة...
        del "Data\GashmiDebtManagement.db"
        echo ✅ تم حذف قاعدة البيانات القديمة
    ) else (
        echo.
        echo تم الاحتفاظ بقاعدة البيانات الموجودة
        goto :migrate
    )
)

echo.
echo جاري إنشاء قاعدة البيانات الجديدة...

REM تشغيل التطبيق لإنشاء قاعدة البيانات
dotnet run --no-build -- --setup-db-only 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إنشاء قاعدة البيانات بنجاح
) else (
    echo ⚠️ تحذير: قد تحتاج لبناء المشروع أولاً
    echo جاري بناء المشروع...
    dotnet build --configuration Release
    if %ERRORLEVEL% EQU 0 (
        echo ✅ تم بناء المشروع
        echo جاري إنشاء قاعدة البيانات...
        dotnet run --configuration Release -- --setup-db-only
    )
)

:migrate
echo.
echo جاري تطبيق التحديثات على قاعدة البيانات...
dotnet ef database update 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم تطبيق التحديثات بنجاح
) else (
    echo ℹ️ لا توجد تحديثات مطلوبة أو EF Tools غير مثبت
)

echo.
echo جاري التحقق من سلامة قاعدة البيانات...

if exist "Data\GashmiDebtManagement.db" (
    echo ✅ قاعدة البيانات متوفرة
    
    REM عرض حجم قاعدة البيانات
    for %%f in (Data\GashmiDebtManagement.db) do (
        echo ├─ الحجم: %%~zf bytes
        echo ├─ تاريخ الإنشاء: %%~tf
    )
    
    echo └─ المسار: %CD%\Data\GashmiDebtManagement.db
) else (
    echo ❌ فشل في إنشاء قاعدة البيانات
    echo.
    echo الحلول المقترحة:
    echo 1. تأكد من تثبيت .NET 8.0 أو أحدث
    echo 2. تأكد من صلاحيات الكتابة في المجلد
    echo 3. جرب تشغيل الأمر كمدير
    echo 4. تأكد من عدم استخدام قاعدة البيانات من تطبيق آخر
    pause
    exit /b 1
)

echo.
echo ========================================
echo    تم إعداد قاعدة البيانات بنجاح!
echo ========================================
echo.
echo 📋 معلومات قاعدة البيانات:
echo ├─ النوع: SQLite
echo ├─ الموقع: Data\GashmiDebtManagement.db
echo ├─ الجداول: Raayah, WeeklyDebts
echo └─ الترميز: UTF-8 (دعم العربية)
echo.
echo 🚀 يمكنك الآن تشغيل التطبيق:
echo    انقر نقراً مزدوجاً على run.bat
echo.

pause
