using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Services;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج تعديل البيانات الأسبوعية
    /// </summary>
    public partial class WeeklyDataEditForm : Form
    {
        private readonly IWeeklyDebtsRepository _weeklyDebtsRepository;

        // عناصر التحكم
        private Panel? selectionPanel;
        private Label? fromDateLabel;
        private DateTimePicker? fromDatePicker;
        private Label? toDateLabel;
        private DateTimePicker? toDatePicker;
        private Button? loadDataButton;
        private Button? deleteWeekButton;
        private DataGridView? dataGridView;
        private Panel? actionPanel;
        private Button? saveButton;
        private Button? cancelButton;
        private Label? statusLabel;

        private List<WeeklyDebts>? originalData;
        private bool hasChanges = false;

        public WeeklyDataEditForm(IWeeklyDebtsRepository weeklyDebtsRepository)
        {
            _weeklyDebtsRepository = weeklyDebtsRepository;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "تعديل البيانات الأسبوعية";
            this.Size = new Size(1300, 700);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);

            // إنشاء لوحة الاختيار
            CreateSelectionPanel();

            // إنشاء جدول البيانات
            CreateDataGridView();

            // إنشاء لوحة الإجراءات
            CreateActionPanel();

            this.ResumeLayout(false);
        }

        private void CreateSelectionPanel()
        {
            selectionPanel = new Panel();
            selectionPanel.Height = 80;
            selectionPanel.Dock = DockStyle.Top;
            selectionPanel.BackColor = Color.LightBlue;

            // تسمية تاريخ البداية
            fromDateLabel = new Label();
            fromDateLabel.Text = "من تاريخ:";
            fromDateLabel.Location = new Point(1100, 25);
            fromDateLabel.Size = new Size(80, 23);
            fromDateLabel.TextAlign = ContentAlignment.MiddleRight;

            // منتقي تاريخ البداية
            fromDatePicker = new DateTimePicker();
            fromDatePicker.Location = new Point(950, 25);
            fromDatePicker.Size = new Size(140, 23);
            fromDatePicker.Format = DateTimePickerFormat.Short;
            fromDatePicker.ValueChanged += FromDatePicker_ValueChanged;

            // تسمية تاريخ النهاية
            toDateLabel = new Label();
            toDateLabel.Text = "إلى تاريخ:";
            toDateLabel.Location = new Point(850, 25);
            toDateLabel.Size = new Size(80, 23);
            toDateLabel.TextAlign = ContentAlignment.MiddleRight;

            // منتقي تاريخ النهاية
            toDatePicker = new DateTimePicker();
            toDatePicker.Location = new Point(700, 25);
            toDatePicker.Size = new Size(140, 23);
            toDatePicker.Format = DateTimePickerFormat.Short;
            toDatePicker.Enabled = false;

            // زر تحميل البيانات
            loadDataButton = new Button();
            loadDataButton.Text = "تحميل للتعديل";
            loadDataButton.Size = new Size(120, 35);
            loadDataButton.Location = new Point(550, 20);
            loadDataButton.BackColor = Color.LightGreen;
            loadDataButton.Click += LoadDataButton_Click;

            // زر حذف الأسبوع
            deleteWeekButton = new Button();
            deleteWeekButton.Text = "حذف الأسبوع";
            deleteWeekButton.Size = new Size(120, 35);
            deleteWeekButton.Location = new Point(420, 20);
            deleteWeekButton.BackColor = Color.LightCoral;
            deleteWeekButton.Enabled = false;
            deleteWeekButton.Click += DeleteWeekButton_Click;

            selectionPanel.Controls.AddRange(new Control[] {
                fromDateLabel, fromDatePicker, toDateLabel, toDatePicker,
                loadDataButton, deleteWeekButton
            });

            this.Controls.Add(selectionPanel);
        }

        private void CreateDataGridView()
        {
            dataGridView = new DataGridView();
            dataGridView.Dock = DockStyle.Fill;
            dataGridView.AutoGenerateColumns = false;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.BackgroundColor = Color.White;
            dataGridView.Font = new Font("Tahoma", 9F);

            // إعداد الأعمدة
            SetupDataGridViewColumns();

            // أحداث
            dataGridView.CellValueChanged += DataGridView_CellValueChanged;
            dataGridView.CellEndEdit += DataGridView_CellEndEdit;

            this.Controls.Add(dataGridView);
        }

        private void SetupDataGridViewColumns()
        {
            // عمود الاسم (للقراءة فقط)
            var nameColumn = new DataGridViewTextBoxColumn();
            nameColumn.Name = "RaayahName";
            nameColumn.HeaderText = "اسم الرعوي";
            nameColumn.DataPropertyName = "Raayah.FullName";
            nameColumn.Width = 200;
            nameColumn.ReadOnly = true;

            // أعمدة الفروع القابلة للتعديل
            var samirColumn = new DataGridViewTextBoxColumn();
            samirColumn.Name = "SamirAmount";
            samirColumn.HeaderText = "سمير";
            samirColumn.DataPropertyName = "SamirAmount";
            samirColumn.Width = 100;
            samirColumn.DefaultCellStyle.Format = "N2";

            var maherColumn = new DataGridViewTextBoxColumn();
            maherColumn.Name = "MaherAmount";
            maherColumn.HeaderText = "ماهر";
            maherColumn.DataPropertyName = "MaherAmount";
            maherColumn.Width = 100;
            maherColumn.DefaultCellStyle.Format = "N2";

            var raidColumn = new DataGridViewTextBoxColumn();
            raidColumn.Name = "RaidAmount";
            raidColumn.HeaderText = "رايد";
            raidColumn.DataPropertyName = "RaidAmount";
            raidColumn.Width = 100;
            raidColumn.DefaultCellStyle.Format = "N2";

            var haiderColumn = new DataGridViewTextBoxColumn();
            haiderColumn.Name = "HaiderAmount";
            haiderColumn.HeaderText = "حيدر";
            haiderColumn.DataPropertyName = "HaiderAmount";
            haiderColumn.Width = 100;
            haiderColumn.DefaultCellStyle.Format = "N2";

            var lateColumn = new DataGridViewTextBoxColumn();
            lateColumn.Name = "LateAmount";
            lateColumn.HeaderText = "متأخر";
            lateColumn.DataPropertyName = "LateAmount";
            lateColumn.Width = 100;
            lateColumn.DefaultCellStyle.Format = "N2";

            var receivedColumn = new DataGridViewTextBoxColumn();
            receivedColumn.Name = "ReceivedAmount";
            receivedColumn.HeaderText = "واصل";
            receivedColumn.DataPropertyName = "ReceivedAmount";
            receivedColumn.Width = 100;
            receivedColumn.DefaultCellStyle.Format = "N2";

            // أعمدة محسوبة (للقراءة فقط)
            var totalColumn = new DataGridViewTextBoxColumn();
            totalColumn.Name = "TotalAmount";
            totalColumn.HeaderText = "الإجمالي";
            totalColumn.DataPropertyName = "TotalDebtsAmount";
            totalColumn.Width = 120;
            totalColumn.ReadOnly = true;
            totalColumn.DefaultCellStyle.Format = "N2";
            totalColumn.DefaultCellStyle.BackColor = Color.LightYellow;

            var discountColumn = new DataGridViewTextBoxColumn();
            discountColumn.Name = "DiscountAmount";
            discountColumn.HeaderText = "الخصم";
            discountColumn.DataPropertyName = "DiscountAmount";
            discountColumn.Width = 100;
            discountColumn.ReadOnly = true;
            discountColumn.DefaultCellStyle.Format = "N2";
            discountColumn.DefaultCellStyle.BackColor = Color.LightCoral;

            var netColumn = new DataGridViewTextBoxColumn();
            netColumn.Name = "NetAmount";
            netColumn.HeaderText = "الصافي";
            netColumn.DataPropertyName = "NetAmount";
            netColumn.Width = 120;
            netColumn.ReadOnly = true;
            netColumn.DefaultCellStyle.Format = "N2";
            netColumn.DefaultCellStyle.BackColor = Color.LightGreen;

            dataGridView!.Columns.AddRange(new DataGridViewColumn[] {
                nameColumn, samirColumn, maherColumn, raidColumn, haiderColumn,
                lateColumn, receivedColumn, totalColumn, discountColumn, netColumn
            });

            // تنسيق الرؤوس
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Navy;
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            dataGridView.ColumnHeadersHeight = 35;

            // تنسيق الصفوف
            dataGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.AliceBlue;
            dataGridView.RowsDefaultCellStyle.BackColor = Color.White;
            dataGridView.RowTemplate.Height = 30;
        }

        private void CreateActionPanel()
        {
            actionPanel = new Panel();
            actionPanel.Height = 80;
            actionPanel.Dock = DockStyle.Bottom;
            actionPanel.BackColor = Color.LightGray;

            // تسمية الحالة
            statusLabel = new Label();
            statusLabel.Text = "اختر فترة وحمل البيانات للتعديل";
            statusLabel.Location = new Point(20, 25);
            statusLabel.Size = new Size(600, 30);
            statusLabel.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            // زر الحفظ
            saveButton = new Button();
            saveButton.Text = "حفظ التعديلات";
            saveButton.Size = new Size(120, 40);
            saveButton.Location = new Point(1050, 20);
            saveButton.BackColor = Color.LightGreen;
            saveButton.Enabled = false;
            saveButton.Click += SaveButton_Click;

            // زر الإلغاء
            cancelButton = new Button();
            cancelButton.Text = "إلغاء التعديلات";
            cancelButton.Size = new Size(120, 40);
            cancelButton.Location = new Point(920, 20);
            cancelButton.BackColor = Color.LightCoral;
            cancelButton.Enabled = false;
            cancelButton.Click += CancelButton_Click;

            actionPanel.Controls.AddRange(new Control[] {
                statusLabel, saveButton, cancelButton
            });

            this.Controls.Add(actionPanel);
        }

        private void FromDatePicker_ValueChanged(object? sender, EventArgs e)
        {
            // حساب تاريخ النهاية تلقائياً
            toDatePicker!.Value = fromDatePicker!.Value.AddDays(6);
        }

        private async void LoadDataButton_Click(object? sender, EventArgs e)
        {
            try
            {
                loadDataButton!.Enabled = false;
                loadDataButton.Text = "جاري التحميل...";

                var fromDate = fromDatePicker!.Value.Date;
                var toDate = toDatePicker!.Value.Date;

                // تحميل البيانات
                var weeklyData = await _weeklyDebtsRepository.GetByPeriodWithRaayahAsync(fromDate, toDate);
                var dataList = weeklyData.OrderBy(d => d.Raayah.FullName).ToList();

                if (!dataList.Any())
                {
                    MessageBox.Show("لا توجد بيانات لهذه الفترة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // حفظ البيانات الأصلية للمقارنة
                originalData = dataList.Select(d => new WeeklyDebts
                {
                    Id = d.Id,
                    RaayahId = d.RaayahId,
                    DateFrom = d.DateFrom,
                    DateTo = d.DateTo,
                    SamirAmount = d.SamirAmount,
                    MaherAmount = d.MaherAmount,
                    RaidAmount = d.RaidAmount,
                    HaiderAmount = d.HaiderAmount,
                    LateAmount = d.LateAmount,
                    ReceivedAmount = d.ReceivedAmount,
                    Raayah = d.Raayah
                }).ToList();

                // عرض البيانات
                dataGridView!.DataSource = dataList;

                // تفعيل الأزرار
                deleteWeekButton!.Enabled = true;
                saveButton!.Enabled = false;
                cancelButton!.Enabled = true;

                // تحديث الحالة
                statusLabel!.Text = $"تم تحميل {dataList.Count} سجل للفترة {fromDate:dd/MM/yyyy} - {toDate:dd/MM/yyyy}";
                hasChanges = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                loadDataButton!.Enabled = true;
                loadDataButton.Text = "تحميل للتعديل";
            }
        }

        private void DataGridView_CellValueChanged(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                hasChanges = true;
                saveButton!.Enabled = true;
                statusLabel!.Text = "تم تعديل البيانات - يرجى الحفظ أو الإلغاء";
                statusLabel.ForeColor = Color.Red;
            }
        }

        private void DataGridView_CellEndEdit(object? sender, DataGridViewCellEventArgs e)
        {
            // التحقق من صحة القيم المدخلة
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                var cell = dataGridView!.Rows[e.RowIndex].Cells[e.ColumnIndex];
                if (cell.Value != null && !decimal.TryParse(cell.Value.ToString(), out _))
                {
                    MessageBox.Show("يرجى إدخال رقم صحيح", "خطأ في الإدخال",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                    // استعادة القيمة الأصلية
                    var debt = dataGridView.Rows[e.RowIndex].DataBoundItem as WeeklyDebts;
                    if (debt != null && originalData != null)
                    {
                        var original = originalData.FirstOrDefault(d => d.Id == debt.Id);
                        if (original != null)
                        {
                            // استعادة القيمة الأصلية حسب العمود
                            switch (dataGridView.Columns[e.ColumnIndex].Name)
                            {
                                case "SamirAmount":
                                    cell.Value = original.SamirAmount;
                                    break;
                                case "MaherAmount":
                                    cell.Value = original.MaherAmount;
                                    break;
                                case "RaidAmount":
                                    cell.Value = original.RaidAmount;
                                    break;
                                case "HaiderAmount":
                                    cell.Value = original.HaiderAmount;
                                    break;
                                case "LateAmount":
                                    cell.Value = original.LateAmount;
                                    break;
                                case "ReceivedAmount":
                                    cell.Value = original.ReceivedAmount;
                                    break;
                            }
                        }
                    }
                }
            }
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من حفظ التعديلات؟",
                    "تأكيد الحفظ", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                    return;

                saveButton!.Enabled = false;
                saveButton.Text = "جاري الحفظ...";

                var data = dataGridView!.DataSource as List<WeeklyDebts>;
                if (data != null)
                {
                    await _weeklyDebtsRepository.UpdateRangeAsync(data);
                    await _weeklyDebtsRepository.SaveChangesAsync();

                    MessageBox.Show("تم حفظ التعديلات بنجاح!", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    hasChanges = false;
                    saveButton.Enabled = false;
                    statusLabel!.Text = "تم حفظ التعديلات بنجاح";
                    statusLabel.ForeColor = Color.Green;

                    // تحديث البيانات الأصلية
                    originalData = data.ToList();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في حفظ التعديلات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                saveButton!.Text = "حفظ التعديلات";
                saveButton.Enabled = hasChanges;
            }
        }

        private void CancelButton_Click(object? sender, EventArgs e)
        {
            if (hasChanges)
            {
                var result = MessageBox.Show("هناك تعديلات غير محفوظة. هل تريد المتابعة؟",
                    "تأكيد الإلغاء", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                if (result != DialogResult.Yes)
                    return;
            }

            // إعادة تحميل البيانات الأصلية
            if (originalData != null)
            {
                dataGridView!.DataSource = originalData.ToList();
                hasChanges = false;
                saveButton!.Enabled = false;
                statusLabel!.Text = "تم إلغاء التعديلات";
                statusLabel.ForeColor = Color.Blue;
            }
        }

        private async void DeleteWeekButton_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف جميع بيانات الأسبوع؟\n" +
                $"الفترة: {fromDatePicker!.Value:dd/MM/yyyy} - {toDatePicker!.Value:dd/MM/yyyy}\n" +
                "هذا الإجراء لا يمكن التراجع عنه!",
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result != DialogResult.Yes)
                return;

            try
            {
                deleteWeekButton!.Enabled = false;
                deleteWeekButton.Text = "جاري الحذف...";

                var fromDate = fromDatePicker.Value.Date;
                var toDate = toDatePicker.Value.Date;

                await _weeklyDebtsRepository.DeleteByPeriodAsync(fromDate, toDate);
                await _weeklyDebtsRepository.SaveChangesAsync();

                MessageBox.Show("تم حذف بيانات الأسبوع بنجاح!", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // مسح العرض
                dataGridView!.DataSource = null;
                originalData = null;
                hasChanges = false;
                saveButton!.Enabled = false;
                cancelButton!.Enabled = false;
                statusLabel!.Text = "تم حذف بيانات الأسبوع";
                statusLabel.ForeColor = Color.Red;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في حذف البيانات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                deleteWeekButton!.Text = "حذف الأسبوع";
                deleteWeekButton.Enabled = originalData?.Any() == true;
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (hasChanges)
            {
                var result = MessageBox.Show("هناك تعديلات غير محفوظة. هل تريد إغلاق النافذة؟",
                    "تأكيد الإغلاق", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                }
            }

            base.OnFormClosing(e);
        }
    }
}
