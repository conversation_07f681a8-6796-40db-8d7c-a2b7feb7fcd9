using System.Drawing;
using System.Windows.Forms;

namespace GashmiDebtManagement.Helpers
{
    /// <summary>
    /// مساعد تحسين واجهة المستخدم
    /// </summary>
    public static class UIHelper
    {
        #region نظام الألوان العصري
        
        /// <summary>
        /// نظام ألوان عصري مناسب للبيئة العربية
        /// </summary>
        public static class ModernColors
        {
            // الألوان الأساسية
            public static readonly Color Primary = Color.FromArgb(41, 128, 185);      // أزرق هادئ
            public static readonly Color Secondary = Color.FromArgb(52, 152, 219);    // أزرق فاتح
            public static readonly Color Success = Color.FromArgb(39, 174, 96);       // أخضر نجاح
            public static readonly Color Warning = Color.FromArgb(241, 196, 15);      // أصفر تحذير
            public static readonly Color Danger = Color.FromArgb(231, 76, 60);        // أحمر خطر
            public static readonly Color Info = Color.FromArgb(142, 68, 173);         // بنفسجي معلومات
            
            // ألوان الخلفية
            public static readonly Color Background = Color.FromArgb(248, 249, 250);   // خلفية فاتحة
            public static readonly Color Surface = Color.White;                        // سطح أبيض
            public static readonly Color Card = Color.FromArgb(255, 255, 255);        // بطاقة بيضاء
            
            // ألوان النص
            public static readonly Color TextPrimary = Color.FromArgb(33, 37, 41);    // نص أساسي
            public static readonly Color TextSecondary = Color.FromArgb(108, 117, 125); // نص ثانوي
            public static readonly Color TextMuted = Color.FromArgb(173, 181, 189);   // نص خافت
            
            // ألوان الحدود
            public static readonly Color Border = Color.FromArgb(222, 226, 230);      // حدود فاتحة
            public static readonly Color BorderDark = Color.FromArgb(173, 181, 189);  // حدود داكنة
            
            // ألوان التفاعل
            public static readonly Color Hover = Color.FromArgb(240, 242, 245);       // تمرير الماوس
            public static readonly Color Active = Color.FromArgb(233, 236, 239);      // نشط
            public static readonly Color Focus = Color.FromArgb(0, 123, 255, 25);     // تركيز شفاف
        }
        
        #endregion
        
        #region الخطوط العصرية
        
        /// <summary>
        /// نظام خطوط محسن للعربية
        /// </summary>
        public static class ModernFonts
        {
            // الخطوط الأساسية
            public static readonly Font HeaderLarge = new Font("Segoe UI", 24F, FontStyle.Bold);
            public static readonly Font HeaderMedium = new Font("Segoe UI", 18F, FontStyle.Bold);
            public static readonly Font HeaderSmall = new Font("Segoe UI", 14F, FontStyle.Bold);
            
            public static readonly Font BodyLarge = new Font("Segoe UI", 12F, FontStyle.Regular);
            public static readonly Font BodyMedium = new Font("Segoe UI", 11F, FontStyle.Regular);
            public static readonly Font BodySmall = new Font("Segoe UI", 10F, FontStyle.Regular);
            
            public static readonly Font Caption = new Font("Segoe UI", 9F, FontStyle.Regular);
            public static readonly Font Button = new Font("Segoe UI", 11F, FontStyle.Regular);
            
            // خطوط عربية محسنة
            public static readonly Font ArabicHeaderLarge = new Font("Segoe UI", 22F, FontStyle.Bold);
            public static readonly Font ArabicHeaderMedium = new Font("Segoe UI", 16F, FontStyle.Bold);
            public static readonly Font ArabicBody = new Font("Segoe UI", 11F, FontStyle.Regular);
            public static readonly Font ArabicCaption = new Font("Segoe UI", 9F, FontStyle.Regular);
        }
        
        #endregion
        
        #region تحسينات التخطيط
        
        /// <summary>
        /// قيم التباعد المعيارية
        /// </summary>
        public static class Spacing
        {
            public const int XSmall = 4;
            public const int Small = 8;
            public const int Medium = 16;
            public const int Large = 24;
            public const int XLarge = 32;
            public const int XXLarge = 48;
        }
        
        /// <summary>
        /// أحجام العناصر المعيارية
        /// </summary>
        public static class Sizes
        {
            public const int ButtonHeight = 36;
            public const int InputHeight = 32;
            public const int ToolbarHeight = 48;
            public const int StatusBarHeight = 24;
            public const int BorderRadius = 6;
        }
        
        #endregion
        
        #region تحسينات الأزرار
        
        /// <summary>
        /// إنشاء زر عصري
        /// </summary>
        public static Button CreateModernButton(string text, Color backgroundColor, Color textColor, EventHandler? clickHandler = null)
        {
            var button = new Button
            {
                Text = text,
                BackColor = backgroundColor,
                ForeColor = textColor,
                Font = ModernFonts.Button,
                FlatStyle = FlatStyle.Flat,
                Height = Sizes.ButtonHeight,
                Cursor = Cursors.Hand,
                UseVisualStyleBackColor = false
            };
            
            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.BorderColor = backgroundColor;
            
            // تأثيرات التفاعل
            button.MouseEnter += (s, e) => {
                button.BackColor = DarkenColor(backgroundColor, 0.1f);
            };
            
            button.MouseLeave += (s, e) => {
                button.BackColor = backgroundColor;
            };
            
            if (clickHandler != null)
                button.Click += clickHandler;
                
            return button;
        }
        
        /// <summary>
        /// إنشاء زر أساسي
        /// </summary>
        public static Button CreatePrimaryButton(string text, EventHandler? clickHandler = null)
        {
            return CreateModernButton(text, ModernColors.Primary, Color.White, clickHandler);
        }
        
        /// <summary>
        /// إنشاء زر نجاح
        /// </summary>
        public static Button CreateSuccessButton(string text, EventHandler? clickHandler = null)
        {
            return CreateModernButton(text, ModernColors.Success, Color.White, clickHandler);
        }
        
        /// <summary>
        /// إنشاء زر خطر
        /// </summary>
        public static Button CreateDangerButton(string text, EventHandler? clickHandler = null)
        {
            return CreateModernButton(text, ModernColors.Danger, Color.White, clickHandler);
        }
        
        /// <summary>
        /// إنشاء زر ثانوي
        /// </summary>
        public static Button CreateSecondaryButton(string text, EventHandler? clickHandler = null)
        {
            return CreateModernButton(text, ModernColors.Border, ModernColors.TextPrimary, clickHandler);
        }
        
        #endregion
        
        #region وظائف مساعدة
        
        /// <summary>
        /// تغميق لون
        /// </summary>
        public static Color DarkenColor(Color color, float factor)
        {
            return Color.FromArgb(
                color.A,
                (int)(color.R * (1 - factor)),
                (int)(color.G * (1 - factor)),
                (int)(color.B * (1 - factor))
            );
        }
        
        /// <summary>
        /// تفتيح لون
        /// </summary>
        public static Color LightenColor(Color color, float factor)
        {
            return Color.FromArgb(
                color.A,
                (int)(color.R + (255 - color.R) * factor),
                (int)(color.G + (255 - color.G) * factor),
                (int)(color.B + (255 - color.B) * factor)
            );
        }
        
        #endregion

        #region تحسينات مربعات النص

        /// <summary>
        /// إنشاء مربع نص عصري
        /// </summary>
        public static TextBox CreateModernTextBox(string placeholder = "")
        {
            var textBox = new TextBox
            {
                Font = ModernFonts.BodyMedium,
                Height = Sizes.InputHeight,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = ModernColors.Surface,
                ForeColor = ModernColors.TextPrimary
            };

            // إضافة placeholder
            if (!string.IsNullOrEmpty(placeholder))
            {
                textBox.Text = placeholder;
                textBox.ForeColor = ModernColors.TextMuted;

                textBox.Enter += (s, e) => {
                    if (textBox.Text == placeholder)
                    {
                        textBox.Text = "";
                        textBox.ForeColor = ModernColors.TextPrimary;
                    }
                };

                textBox.Leave += (s, e) => {
                    if (string.IsNullOrWhiteSpace(textBox.Text))
                    {
                        textBox.Text = placeholder;
                        textBox.ForeColor = ModernColors.TextMuted;
                    }
                };
            }

            return textBox;
        }

        #endregion

        #region تحسينات اللوحات

        /// <summary>
        /// إنشاء لوحة بطاقة عصرية
        /// </summary>
        public static Panel CreateCardPanel()
        {
            var panel = new Panel
            {
                BackColor = ModernColors.Card,
                Padding = new Padding(Spacing.Medium),
                Margin = new Padding(Spacing.Small)
            };

            // إضافة حدود ناعمة (محاكاة)
            panel.Paint += (s, e) => {
                var rect = new Rectangle(0, 0, panel.Width - 1, panel.Height - 1);
                using (var pen = new Pen(ModernColors.Border))
                {
                    e.Graphics.DrawRectangle(pen, rect);
                }
            };

            return panel;
        }

        /// <summary>
        /// إنشاء لوحة أدوات عصرية
        /// </summary>
        public static Panel CreateToolbarPanel()
        {
            return new Panel
            {
                Height = Sizes.ToolbarHeight,
                Dock = DockStyle.Top,
                BackColor = ModernColors.Surface,
                Padding = new Padding(Spacing.Medium, Spacing.Small, Spacing.Medium, Spacing.Small)
            };
        }

        #endregion

        #region تحسينات الجداول

        /// <summary>
        /// تطبيق تصميم عصري على DataGridView
        /// </summary>
        public static void ApplyModernStyle(DataGridView dataGridView)
        {
            // الألوان الأساسية
            dataGridView.BackgroundColor = ModernColors.Background;
            dataGridView.GridColor = ModernColors.Border;
            dataGridView.BorderStyle = BorderStyle.None;

            // تصميم الرأس
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = ModernColors.Primary;
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.Font = ModernFonts.HeaderSmall;
            dataGridView.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridView.ColumnHeadersHeight = 40;
            dataGridView.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;

            // تصميم الصفوف
            dataGridView.DefaultCellStyle.BackColor = ModernColors.Surface;
            dataGridView.DefaultCellStyle.ForeColor = ModernColors.TextPrimary;
            dataGridView.DefaultCellStyle.Font = ModernFonts.BodyMedium;
            dataGridView.DefaultCellStyle.SelectionBackColor = ModernColors.Secondary;
            dataGridView.DefaultCellStyle.SelectionForeColor = Color.White;
            dataGridView.RowTemplate.Height = 35;

            // تصميم الصفوف المتناوبة
            dataGridView.AlternatingRowsDefaultCellStyle.BackColor = ModernColors.Background;

            // إعدادات إضافية
            dataGridView.EnableHeadersVisualStyles = false;
            dataGridView.AllowUserToResizeRows = false;
            dataGridView.RowHeadersVisible = false;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.MultiSelect = false;
        }

        #endregion

        #region تحسينات القوائم

        /// <summary>
        /// تطبيق تصميم عصري على MenuStrip
        /// </summary>
        public static void ApplyModernStyle(MenuStrip menuStrip)
        {
            menuStrip.BackColor = ModernColors.Surface;
            menuStrip.ForeColor = ModernColors.TextPrimary;
            menuStrip.Font = ModernFonts.BodyMedium;

            // تطبيق التصميم على جميع العناصر
            foreach (ToolStripMenuItem item in menuStrip.Items)
            {
                ApplyModernStyleToMenuItem(item);
            }
        }

        private static void ApplyModernStyleToMenuItem(ToolStripMenuItem item)
        {
            item.BackColor = ModernColors.Surface;
            item.ForeColor = ModernColors.TextPrimary;

            // تطبيق على العناصر الفرعية
            foreach (ToolStripItem subItem in item.DropDownItems)
            {
                if (subItem is ToolStripMenuItem menuItem)
                {
                    ApplyModernStyleToMenuItem(menuItem);
                }
            }
        }

        #endregion

        #region اختصارات لوحة المفاتيح

        /// <summary>
        /// إضافة اختصارات لوحة المفاتيح للنموذج
        /// </summary>
        public static void AddKeyboardShortcuts(Form form)
        {
            form.KeyPreview = true;
            form.KeyDown += (s, e) => {
                switch (e.KeyCode)
                {
                    case Keys.F1:
                        // مساعدة
                        ShowHelp();
                        break;
                    case Keys.F5:
                        // تحديث
                        RefreshForm(form);
                        break;
                    case Keys.Escape:
                        // إغلاق
                        form.Close();
                        break;
                    case Keys.S when e.Control:
                        // حفظ
                        SaveForm(form);
                        break;
                    case Keys.N when e.Control:
                        // جديد
                        NewRecord(form);
                        break;
                }
            };
        }

        private static void ShowHelp()
        {
            MessageBox.Show("F1: مساعدة\nF5: تحديث\nEsc: إغلاق\nCtrl+S: حفظ\nCtrl+N: جديد",
                "اختصارات لوحة المفاتيح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private static void RefreshForm(Form form)
        {
            // تحديث النموذج
            form.Refresh();
        }

        private static void SaveForm(Form form)
        {
            // البحث عن زر الحفظ وتنفيذه
            var saveButton = FindControlByName(form, "saveButton") as Button;
            saveButton?.PerformClick();
        }

        private static void NewRecord(Form form)
        {
            // البحث عن زر إضافة جديد وتنفيذه
            var addButton = FindControlByName(form, "addButton") as Button;
            addButton?.PerformClick();
        }

        private static Control? FindControlByName(Control parent, string name)
        {
            foreach (Control control in parent.Controls)
            {
                if (control.Name == name)
                    return control;

                var found = FindControlByName(control, name);
                if (found != null)
                    return found;
            }
            return null;
        }

        #endregion
    }
}
